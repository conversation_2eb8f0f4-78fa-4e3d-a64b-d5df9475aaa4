package com.czur.starry.device.file.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.starry.device.baselib.tips.setCustomFloatTip
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.FileEntity
import com.czur.uilib.choose.CZCheckBox

/**
 * 封装文件列表的图标
 */
private const val MODE_GRID = 0
private const val MODE_LIST = 1

class FileItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr), View.OnHoverListener {

    private lateinit var fileItemSelectIv: ImageView
    private lateinit var fileItemNameTv: TextView
    private lateinit var fileItemIconIv: IconImageView

    lateinit var iconItemIconIv: IconImageView
        private set
    lateinit var iconItemUploadIv: ImageFilterView
        private set
    private var fileItemListCheck: CZCheckBox? = null

    companion object {
        private const val SCALE_ANIM_DURATION = 200L
        private const val SCARE_ANIM_RATE = 1.1F
    }

    /**
     * 显示模式:
     * [MODE_GRID] 和 [MODE_LIST]
     */
    var showMode = MODE_GRID
        set(value) {
            if (field != value) {
                field = value
                initViews()
            }
        }

    /**
     * 可编辑:
     */
    var isSelectMode = false
        set(value) {
            if (field != value) {
                field = value
                refreshSelectModeUI()
            }
        }

    /**
     * 上传状态
     */
    var isUploadStatus = 0
        set(value) {
            if (field != value) {
                field = value
                if (value == 0) {
                    fileEntity?.lastModifyTime = System.currentTimeMillis()
                    changeIconByEntity(fileEntity)
                }
                refreshUploadUI()
            }
        }

    override fun setSelected(selected: Boolean) {
        if (selected != isSelected) {
            super.setSelected(selected)
            fileItemSelectIv.visibility =
                if (showMode == MODE_GRID && selected) VISIBLE else INVISIBLE
            fileItemListCheck?.setChecked(selected)
        }
    }

    // 标题
    private var fileName = ""
        set(value) {
            fileItemNameTv.text = value
            checkAndSetFloatTip()
            field = value
        }

    /**
     * 文件,
     * 改变FileItemView的该属性, 会自动修改对应的图标和文字
     */
    var fileEntity: FileEntity? = null
        set(value) {
            if (field != value) {
                field = value
                changeIconByEntity(value)
                fileName = value?.name ?: fileName
            }
        }

    init {
        initViews()
        setOnHoverListener(this)
        setBackgroundColor(0)
    }

    private fun initViews() {
        removeAllViews()

        val layout = when (showMode) {
            MODE_GRID -> R.layout.item_file_icon_grid
            else -> R.layout.item_file_icon_list
        }
        inflate(context, layout, this)
        val params = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT, // width
            FrameLayout.LayoutParams.WRAP_CONTENT  // height
        )
        if (showMode == MODE_LIST) {
            params.leftMargin = 50
        } else {
            params.leftMargin = 0
        }
        this.layoutParams = params
        // 因为引入了自己的Fw.jar, 所以只能这么写W
        fileItemSelectIv = findViewById(R.id.fileItemSelectIv) as ImageView
        fileItemNameTv = findViewById(R.id.fileItemNameTv) as TextView
        iconItemIconIv = findViewById(R.id.fileItemIconIv) as IconImageView
        iconItemUploadIv = findViewById(R.id.fileItemUploadIv) as ImageFilterView
        fileItemListCheck = findViewById(R.id.fileItemListCheck) as CZCheckBox?
        fileItemIconIv = findViewById(R.id.fileItemIconIv) as IconImageView

        fileItemListCheck?.isEnabled = false

        fileItemNameTv.text = fileName
        checkAndSetFloatTip()

        changeIconByEntity(fileEntity)
        fileItemSelectIv.visibility =
            if (showMode == MODE_GRID && isSelected) VISIBLE else INVISIBLE
        refreshSelectModeUI()
        refreshUploadUI()
    }

    private fun refreshSelectModeUI() {
        fileItemListCheck?.visibility = if (isSelectMode) VISIBLE else INVISIBLE
    }

    private fun refreshUploadUI() {
        when (isUploadStatus) {
            0 -> {
                iconItemUploadIv.gone()
            }

            1 -> {
                iconItemUploadIv.setImageResource(R.drawable.icon_upload_on)
                iconItemUploadIv.show()
            }

            2 -> {
                iconItemUploadIv.setImageResource(R.drawable.icon_upload_pause)
                iconItemUploadIv.show()
            }
        }
    }


    private fun changeIconByEntity(fileType: FileEntity?) {
        fileType?.let {
            iconItemIconIv.post {
                iconItemIconIv.changeIconByEntity(it)
            }
        }
    }


    override fun onHover(v: View?, event: MotionEvent?): Boolean {
        if (event == null) {
            return false
        }
        when (showMode) {
            MODE_GRID -> setGridHoverAnim(event)
            MODE_LIST -> setListHoverAnim(event)
        }
        return true
    }

    /**
     * 设置List模式的鼠标效果
     * 修改背景颜色
     */
    private fun setListHoverAnim(event: MotionEvent) {
        when (event.action) {
            MotionEvent.ACTION_HOVER_EXIT -> setBackgroundColor(0)
            MotionEvent.ACTION_HOVER_ENTER -> setBackgroundColor(context.getColor(R.color.color_list_item_hover))
        }
    }

    /**
     * 设置Grid模式下的鼠标效果
     * 放大/缩小图标
     */
    private fun setGridHoverAnim(event: MotionEvent) {
        fun View.startScaleAnim(toScale: Float = 1F) {
            animate().scaleX(toScale).scaleY(toScale).setDuration(SCALE_ANIM_DURATION).start()
        }

        when (event.action) {
            MotionEvent.ACTION_HOVER_ENTER -> {
                fileItemSelectIv.startScaleAnim(SCARE_ANIM_RATE)
                fileItemIconIv.startScaleAnim(SCARE_ANIM_RATE)
            }

            MotionEvent.ACTION_HOVER_EXIT -> {
                fileItemSelectIv.startScaleAnim()
                fileItemIconIv.startScaleAnim()
            }
        }
    }

    /**
     * 检查文字是否可以显示全
     * 如果可显示全, 则不设置FloatTip
     * 如果不能显示全, 则显示文件名
     */
    private fun checkAndSetFloatTip() {
        fileItemNameTv.post {
            fileItemNameTv.layout?.let { l ->
                val lines = l.lineCount
                if (lines > 0 && l.getEllipsisCount(lines - 1) > 0) {
                    fileItemNameTv.setCustomFloatTip(fileItemNameTv.text as? String ?: "")
                } else {
                    fileItemNameTv.setCustomFloatTip("")
                }
            }
        }

    }

}