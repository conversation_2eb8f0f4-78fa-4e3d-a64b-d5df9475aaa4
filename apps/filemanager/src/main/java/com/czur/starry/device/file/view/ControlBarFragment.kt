package com.czur.starry.device.file.view

import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import androidx.core.view.forEach
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.Constants.CLICK_DEBOUNCE
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.databinding.FragmentControlBarBinding
import com.czur.starry.device.file.filelib.FileHandlerLive.currentSortTypeLive
import com.czur.starry.device.file.filelib.FileHandlerLive.currentSortTypeState
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.manager.FileShowInfoManager
import com.czur.starry.device.file.utils.isTypeOf
import com.czur.starry.device.file.view.activity.FileMainPageActivity.Companion.changeSelect
import com.czur.starry.device.file.view.vm.MainViewModel
import com.czur.starry.device.file.widget.createSortPopup
import com.wanglu.lib.BasePopup
import com.wanglu.lib.WPopupDirection

/**
 * Created by 陈丰尧 on 12/28/20
 */
class ControlBarFragment :
    CZViewBindingFragment<FragmentControlBarBinding>(), IControlBar {
    private var filePad: IFilePad? = null
        get() {
            if (field == null) {
                logTagW(TAG, "ControlBarFragment 似乎经历了重建, 已经无法使用")
            }
            return field
        }

    companion object {
        private const val TAG = "ControlBarFragment"
        private const val SHOW_TYPE_KEY = "showType"
        const val TYPE_OTHER = 2
        const val TYPE_LOCAL_MEETING = 3
        const val TYPE_AI_TRANS = 4
        const val TYPE_PHOTO_SCREEN_SHORT = 5

        fun getInstance(showType: Int, filePad: IFilePad): ControlBarFragment {
            val instance = ControlBarFragment()
            instance.filePad = filePad
            val bundle = Bundle().apply {
                putInt(SHOW_TYPE_KEY, showType)
            }
            instance.arguments = bundle
            return instance
        }
    }

    private var showType = TYPE_OTHER

    private lateinit var fileEntity: FileEntity

    private val filterModes =
        listOf(
            FileAccess.FilterMode.ALL,
            FileAccess.FilterMode.DOC,
            FileAccess.FilterMode.PIC,
            FileAccess.FilterMode.MEDIA
        )

    private var sortPopup: BasePopup? = null

    // 是否是选择模式
    private var selectMode = false
        // 更新显示模式时, 切换UI模式
        set(value) {
            field = value
            if (value) {
                // selectMode
                binding.controlBarView.gone()
                binding.controlBarSelect.root.show()
            } else {
                binding.controlBarView.show()
                binding.controlBarSelect.root.gone()
                binding.controlBarSelect.selAllCb.setCheckState(com.czur.uilib.choose.CZMultiStateCheckBox.CheckStatus.UNCHECKED)
                selectState = 0 // 重置为未选中状态
                // 清空选择栏的Hover的Background
                binding.controlBarSelect.actionIvLl.forEach {
                    if (it is ImageView) {
                        it.background = null
                    }
                }
            }
        }

    var hideSelBar = false  // 强制隐藏分类栏

    // 是否需要登录
    private val needLogin by lazy {
        filePad?.needLogin() ?: false
    }

    // 选择状态：0=未选中，1=全选，2=半选
    private val selectStateLive = DifferentLiveData(0)
    private var selectState: Int by LiveDataDelegate(selectStateLive)

    private val hoverListener = View.OnHoverListener { view, motionEvent ->
        val imageView = view as? ImageView ?: return@OnHoverListener false
        when (motionEvent.action) {
            MotionEvent.ACTION_HOVER_ENTER -> {
                imageView.background = context?.getDrawable(R.drawable.bg_controlbar_hover)
            }

            MotionEvent.ACTION_HOVER_EXIT -> {
                imageView.background = null
            }
        }
        true
    }

    override var controlBarEnable: Boolean = true
        set(value) {
            field = value
            if (hasActiveView) {
                updateEnableUI()
            }
        }

    override fun FragmentParams.initFragmentParams() {
        viewLifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                onHide()
            }
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            onHide()
        }
    }

    override fun FragmentControlBarBinding.initBindingViews() {
        arguments?.let {
            showType = it.getInt(SHOW_TYPE_KEY, TYPE_OTHER)
        }
        updateEnableUI()
        // 初始化布局结构
        initLayout()
        // 初始化selectBar
        initSelectBar()
        // 初始化监听
        initListener()

        selectStateLive.observe(viewLifecycleOwner) { state ->
            // 设置复选框状态：0=未选中，1=全选，2=半选
            val checkStatus = when(state) {
                0 -> com.czur.uilib.choose.CZMultiStateCheckBox.CheckStatus.UNCHECKED
                1 -> com.czur.uilib.choose.CZMultiStateCheckBox.CheckStatus.CHECKED
                2 -> com.czur.uilib.choose.CZMultiStateCheckBox.CheckStatus.HALF_CHECKED
                else -> com.czur.uilib.choose.CZMultiStateCheckBox.CheckStatus.UNCHECKED
            }
            binding.controlBarSelect.selAllCb.setCheckState(checkStatus)
        }

        currentSortTypeLive.observe(viewLifecycleOwner) {
            // 更新排序类型
            logTagD(TAG, "==========更新排序类型: $it")
            if (it != -1) {
                onPopupItemClick(FileAccess.SortType.values()[it])
                currentSortTypeState = -1
            }
        }

    }

    private fun initLayout() {
        if (hideSelBar) {
            binding.selectBar.visibility = View.GONE
        }

        when (showType) {
            TYPE_LOCAL_MEETING -> {
                // 本地会议记录页面
                binding.selectBar.gone()
                binding.changeLayoutIv.gone()
                binding.addFolderIv.gone()
                binding.controlBarSelect.openMethodIv.gone()
            }

            TYPE_AI_TRANS -> {
                // AI转录页面
                binding.selectBar.gone()
                binding.changeLayoutIv.gone()
                binding.addFolderIv.gone()
                binding.controlBarSelect.openMethodIv.gone()
                binding.controlBarSelect.aiTransDownloadIv.show()
                binding.controlBarSelect.copyIv.gone()
                binding.controlBarSelect.moveIv.gone()
            }
        }
    }

    private fun updateEnableUI() {
        if (controlBarEnable) {
            binding.viewControlBar.alpha = 1F
        } else {
            binding.viewControlBar.alpha = 0.3F
        }
    }

    /**
     * 初始化监听
     */
    private fun initListener() {

        binding.backIv.setOnClickListener {
            // 返回上一级
            filePad?.backToTop()
        }

        binding.changeLayoutIv.loginClick(hoverListener = hoverListener, needLogin, ONE_SECOND) {
            if (!controlBarEnable) return@loginClick
            // 切换布局模式
            launch {
                FileShowInfoManager.changePadMode()
            }
        }

        binding.sortIv.loginClick(hoverListener = hoverListener, needLogin) {
            if (!controlBarEnable) return@loginClick
            launch {
                sortPopup = createSortPopup(
                    requireContext(),
                    defSortType = FileShowInfoManager.loadSortType(filePad!!.getBelongTo()),
                    onPopupItemClick = ::onPopupItemClick
                ).apply {
                    // 显示排序Popup
                    showAtDirectionByViewAlignLeft(it, WPopupDirection.BOTTOM)
                }
            }
        }

        // 添加文件夹 单独增加 50ms的屏蔽
        binding.addFolderIv.loginClick(
            hoverListener = hoverListener,
            needLogin,
            debounceTime = CLICK_DEBOUNCE + 50
        ) {
            if (!controlBarEnable) return@loginClick
            filePad?.createNewFolder()
        }

        // 切换到选择模式
        binding.selectModeIv.loginClick(hoverListener = hoverListener, needLogin) {
            if (!controlBarEnable) return@loginClick
            changeSelectMode(true)
        }

        binding.controlBarSelect.selCancelIv.setOnHoverListener(hoverListener)
        // 取消选择模式
        binding.controlBarSelect.selCancelIv.setOnClickListener {
            if (!controlBarEnable) return@setOnClickListener
            changeSelectMode(false)
        }

        // 选择全部 / 取消选择全部
        binding.controlBarSelect.selAllCb.setOnCheckedChangeListener { state, fromUser ->
            if (fromUser && controlBarEnable){// 如果是用户手动点击,需要处理对应数据

                when (state) {
                    com.czur.uilib.choose.CZMultiStateCheckBox.CheckStatus.CHECKED -> {
                        filePad?.selectAll()
                        // 直接更新状态，确保与列表选择状态一致
                    }
                    com.czur.uilib.choose.CZMultiStateCheckBox.CheckStatus.HALF_CHECKED -> {
                        // 半选状态，不对数据处理
                    }
                    com.czur.uilib.choose.CZMultiStateCheckBox.CheckStatus.UNCHECKED -> {
                        filePad?.selectNone()
                    }
                }
            }
        }

        // 删除选中
        binding.controlBarSelect.delSelIv.loginClick(hoverListener = hoverListener, needLogin) {
            if (!controlBarEnable) return@loginClick
            launch {
                filePad?.deleteSelect()
                changeSelectMode(false)
            }

        }

        // 打开方式按钮
        binding.controlBarSelect.openMethodIv.loginClick(hoverListener = hoverListener, needLogin) {
            if (!controlBarEnable) return@loginClick
            filePad?.openMethod()
            changeSelectMode(false)
        }

        // 重命名选中的文件
        binding.controlBarSelect.renameIv.loginClick(hoverListener = hoverListener, needLogin) {
            if (!controlBarEnable) return@loginClick
            filePad?.renameSelect()
            changeSelectMode(false)
        }

        // 复制文件
        binding.controlBarSelect.copyIv.loginClick(hoverListener = hoverListener, needLogin) {
            if (!controlBarEnable) return@loginClick
            filePad?.copyFiles()
            changeSelectMode(false)
        }
        // 移动文件
        binding.controlBarSelect.moveIv.loginClick(hoverListener = hoverListener, needLogin) {
            if (!controlBarEnable) return@loginClick
            filePad?.moveFiles()
            changeSelectMode(false)
        }

        changeSelect.observe(this) {
            if (it) changeSelectMode(false)
        }

        // 下载文件
        binding.controlBarSelect.aiTransDownloadIv.loginClick(
            hoverListener = hoverListener,
            needLogin
        ) {
            if (!controlBarEnable) return@loginClick
            filePad?.downloadFile(null)
            changeSelectMode(false)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(FileShowInfoManager.padModeFlow) { padMode ->
            // 切换图标的显示
            if (padMode) {
                binding.changeLayoutIv.setImageResource(R.drawable.ic_change_layout_list)
            } else {
                binding.changeLayoutIv.setImageResource(R.drawable.ic_grid_tmp)
            }
        }
    }

    private fun changeSelectMode(newMode: Boolean) {
        if (selectMode == newMode) {
            // 如果值没有改变,则直接退出方法
            return
        }
        selectMode = newMode
        filePad?.changeUIMode(selectMode) // 通知FilePad切换显示模式
        if (selectMode) {
            // 更新初始值
            updateSelectCount(0, 1)
        }
    }

    /**
     * 排序PopupItem的点击事件
     */
    private fun onPopupItemClick(sortType: FileAccess.SortType) {
        launch {
            if (FileShowInfoManager.loadSortType(filePad!!.getBelongTo()) != sortType) {
                // 去除重复的排序请求
                filePad?.sortBy(sortType)
                FileShowInfoManager.updateSortType(filePad!!.getBelongTo(), sortType)
            }
        }
    }


    // 初始化过滤选择条
    private fun initSelectBar() {
        val titles = resources.getStringArray(R.array.select_bar_items).toList()
        binding.selectBar.setTitles(titles)
        // 更新选中页的UI
        fun updateUI(selPos: Int) {
            val mode = filterModes[selPos]
            logTagV(TAG, "切换Filter:${mode}")
            filePad?.showFilter(mode)
            updateNewFolderUI(mode)
        }

        binding.selectBar.setOnSelChangeListener { selPos ->
            updateUI(selPos)
        }
        updateUI(0)
    }

    /**
     * 判断新建文件夹功能是否可用
     */
    private fun updateNewFolderUI(filterMode: FileAccess.FilterMode) {
        binding.addFolderIv.alpha = getAlpha(filterMode == FileAccess.FilterMode.ALL)
    }

    /**
     * 更新显示模式下的UI,
     * 显示分类标签还是文件夹名称
     */
    private fun updateUI() {
        if (fileEntity isTypeOf FileType.ROOT) {
            // 根路径只显示选择栏
            binding.titleBar.visibility = View.GONE
            binding.backIv.visibility = View.GONE
            binding.selectBar.visibility = if (hideSelBar) View.GONE else View.VISIBLE
        } else {
            // 显示选择栏
            binding.titleBar.visibility = View.VISIBLE
            binding.backIv.visibility = View.VISIBLE
            binding.titleTv.text = fileEntity.name

            binding.selectBar.visibility = View.GONE
        }

    }

    /**
     * 设置FilePad
     */
    override fun setFilePad(iFilePad: IFilePad) {
        filePad = iFilePad
    }

    /**
     * 更新当前的FileEntity信息
     */
    override fun updateFileEntity(fileEntity: FileEntity) {
        if (isDetached || isRemoving) {
            logTagW(TAG, "controlBar已经被销毁, 不再执行更新UI操作")
            return
        }
        this.fileEntity = fileEntity
        doWithoutCatch {
            updateUI()
        }
    }

    /**
     * 更新选择的数量
     */
    override fun updateSelectCount(selCount: Int, sumCount: Int, hasUploadFile: Boolean) {
        // 更新选择状态：0=未选中，1=全选，2=半选
        selectState = when (selCount) {
            0 -> 0 // 未选中
            sumCount -> 1 // 全选
            else -> 2 // 半选
        }

        val str = getString(R.string.str_selected_file, selCount)
        binding.controlBarSelect.selCountTv.text = str
        // 只有选择了1个文件时,才允许重命名按钮使用
        binding.controlBarSelect.renameIv.alpha = getAlpha(selCount == 1 && !hasUploadFile)
        // 只有选中了任意一个文件后, 才可以使用复制功能
        binding.controlBarSelect.copyIv.alpha = getAlpha(selCount > 0 && !hasUploadFile)
        binding.controlBarSelect.delSelIv.alpha = getAlpha(selCount > 0)
        binding.controlBarSelect.moveIv.alpha = getAlpha(selCount > 0 && !hasUploadFile)
        binding.controlBarSelect.aiTransDownloadIv.alpha = getAlpha(selCount > 0 && !hasUploadFile)

        binding.controlBarSelect.openMethodIv.alpha = getAlpha(selCount == 1 && !hasUploadFile)
    }

    override fun onEmptyDataChanged(isEmpty: Boolean) {
        super.onEmptyDataChanged(isEmpty)
        binding.controlBarSelect.selAllCb.isEnabled = !isEmpty
    }

    private fun getAlpha(isEnabled: Boolean): Float {
        return if (isEnabled) 1F else 0.3F
    }

    override fun resetSelMode() {
        selectState = 0 // 重置为未选中状态
        changeSelectMode(false)
    }

    override fun enterSelMode() {
        changeSelectMode(true)
    }

    private fun onHide() {
        sortPopup?.dismiss()
        sortPopup = null
    }


}

private fun View.loginClick(
    hoverListener: View.OnHoverListener?,
    needLogin: Boolean,
    debounceTime: Long = CLICK_DEBOUNCE,
    clickListener: (view: View) -> Unit
) {
    setOnHoverListener(hoverListener)
    setOnDebounceClickListener(debounceTime = debounceTime) {
        if (alpha < 1F) return@setOnDebounceClickListener
        if (!needLogin || UserHandler.isLogin) {
            clickListener(it)
        } else {
            toast(R.string.toast_need_login)
        }
    }
}