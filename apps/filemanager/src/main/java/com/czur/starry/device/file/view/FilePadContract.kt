package com.czur.starry.device.file.view

import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.server.entity.AITransFileEntity
import kotlinx.coroutines.Job


/**
 * Created by 陈丰尧 on 12/28/20
 */
interface IControlBar {
    var controlBarEnable: Boolean

    /**
     * 设置FilePad
     */
    fun setFilePad(iFilePad: IFilePad)

    /**
     * 更新当前的FileEntity信息
     */
    fun updateFileEntity(fileEntity: FileEntity)

    /**
     * 更新选中的文件数量
     * @param selCount: 已选中的数量
     */
    fun updateSelectCount(selCount: Int, sumCount: Int, hasUploadFile: Boolean = false)

    /**
     * 还原选择模式
     */
    fun resetSelMode()

    /**
     * 进入选择模式
     */
    fun enterSelMode()

    /**
     * 是否有数据
     */
    fun onEmptyDataChanged(isEmpty:Boolean) {}
}

interface IFilePad {
    var copyJob: Job?

    /**
     * 设置ControlBar
     */
    fun setControlBar(iControlBar: IControlBar) {}

    /**
     * 返回上一级文件夹
     */
    fun backToTop() {}

    /**
     * 过滤显示
     */
    fun showFilter(mode: FileAccess.FilterMode) {}

    /**
     * 按照指定排序规则排序显示
     * @param sortType 排序规则
     */
    fun sortBy(sortType: FileAccess.SortType) {}

    /**
     * 创建新的文件夹
     */
    fun createNewFolder() {}

    /**
     * 切换UI显示模式
     * @param selectMode 是否是选择模式
     */
    fun changeUIMode(selectMode: Boolean) {}

    /**
     * 全选
     */
    fun selectAll() {}

    /**
     * 取消全选
     */
    fun selectNone()

    /**
     * 删除选中的文件
     */
    suspend fun deleteSelect() {}

    /**
     * 重命名选中的文件
     */
    fun renameSelect() {}

    /**
     * 复制选中的文件
     */
    fun copyFiles() {}

    /**
     * 移动选中的文件
     */
    fun moveFiles() {}

    /**
     * 取消复制文件
     */
    fun cancelJob() {}

    /**
     * 文件操作是否需要登录
     */
    fun needLogin(): Boolean = false

    /**
     * 获取当前是属于哪
     */
    fun getBelongTo(): AccessType
    fun openMethod() {}

    /**
     * 下载文件(暂时ai互译使用)
     */
    fun downloadFile(targetList: List<AITransFileEntity>?) {}
}