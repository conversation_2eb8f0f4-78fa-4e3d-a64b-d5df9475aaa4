package com.czur.starry.device.file.widget

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.file.R
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class EmptyRecyclerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {
    private var emptyView: View? = null
    var needCheckNetwork: Boolean = false
    var isUsbTab: Boolean = false

    // 语音转写，无数据文案/图片
    var isFileTransferTab: Boolean = false

    // 是否立即显示
    var isShowNow: Boolean = false

    private val checkEmptyFlow = MutableStateFlow(0L)

    val isEmptyFlow = checkEmptyFlow.map {
        isEmpty()
    }.stateIn(MainScope(), SharingStarted.Lazily, true)

    private val observer: AdapterDataObserver = object : AdapterDataObserver() {
        override fun onChanged() {
            updateCheckFlow()
        }

        override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
            updateCheckFlow()
        }

        override fun onItemRangeRemoved(positionStart: Int, itemCount: Int) {
            updateCheckFlow()
        }
    }

    init {
        requireLifecycleOwner().launch {
            checkEmptyFlow.debounce(10L).collect {
                checkIfEmpty()
            }
        }
    }

    private fun updateCheckFlow() {
        checkEmptyFlow.value = System.currentTimeMillis()
    }

    private fun checkIfEmpty() {
        if (isEmpty()) {
            // 是否需要展示未登录
            emptyView?.let {
                val iv: ImageView = it.findViewById(R.id.noFileIv) as ImageView
                val tv: TextView = it.findViewById(R.id.noFileTv) as TextView
                var color = ContextCompat.getColor(context, R.color.text_common_light) // 获取颜色
                if (needCheckNetwork && !isNetworkConnected()) {
                    iv.setImageResource(R.drawable.ic_empty_no_network)
                    tv.setText(R.string.hint_no_network)
                } else if (isUsbTab) {
                    iv.setImageResource(R.drawable.ic_no_usb_file)
                    tv.setText(R.string.hint_no_file)
                } else if (isFileTransferTab) {
                    iv.setImageResource(R.drawable.ic_no_transfer_file)
                    tv.setText(R.string.str_file_transfer_no_file)
                    color = ContextCompat.getColor(context, R.color.text_white)
                } else {
                    iv.setImageResource(R.drawable.ic_no_file)
                    tv.setText(R.string.hint_no_file)
                }
                tv.setTextColor(color) // 颜色
                when {
                    isFileTransferTab && isShowNow -> it.show()
                    isFileTransferTab && !isShowNow -> it.gone()
                    else -> it.show()
                }
            }
        } else {
            emptyView?.gone()
        }
    }

    private fun isEmpty(): Boolean {
        return adapter?.itemCount ?: 0 == 0
    }

    override fun setAdapter(adapter: Adapter<*>?) {
        val oldAdapter = getAdapter()
        oldAdapter?.unregisterAdapterDataObserver(observer)
        super.setAdapter(adapter)
        adapter?.registerAdapterDataObserver(observer)
        updateCheckFlow()
    }

    /**
     * 添加空布局
     * @param resLayout 空布局ID
     * 注意: 使用这个方法添加的空布局, Recycler必须被FrameLayout所包裹
     */
    fun setEmptyView(
        isUsb: Boolean = false,
        isFileTransfer: Boolean = false,
    ) {
        isUsbTab = isUsb
        isFileTransferTab = isFileTransfer
        if (parent is FrameLayout) {
            emptyView?.let {
                (parent as FrameLayout).removeView(emptyView)
            }
            emptyView = LayoutInflater.from(context)
                .inflate(R.layout.empty_no_file, (parent as FrameLayout), false)
            emptyView?.let {
                it.gone()
                (parent as FrameLayout).addView(it)
            }
            updateCheckFlow()
        }

    }

    fun showEmptyViewWithStatus(status: Boolean) {
        isShowNow = status
        updateCheckFlow()
    }

}