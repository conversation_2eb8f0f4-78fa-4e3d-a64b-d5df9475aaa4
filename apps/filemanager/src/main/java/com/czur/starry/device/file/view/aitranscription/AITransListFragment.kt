package com.czur.starry.device.file.view.aitranscription

import android.os.Bundle
import android.os.FileObserver
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.asFlow
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.common.ResCode
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.doOnItemLongClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.file.R
import com.czur.starry.device.file.base.RefreshAble
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.databinding.FragmentFilePadAiTransBinding
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.manager.AITransAccess
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.manager.FileShowInfoManager
import com.czur.starry.device.file.server.entity.AITransFileEntity
import com.czur.starry.device.file.server.entity.GENERATING_IN_PROGRESS
import com.czur.starry.device.file.server.entity.safeCopy
import com.czur.starry.device.file.utils.TargetFileObserver
import com.czur.starry.device.file.utils.open
import com.czur.starry.device.file.view.IControlBar
import com.czur.starry.device.file.view.IFilePad
import com.czur.starry.device.file.view.dialog.AITransShareQRCodeFloat
import com.czur.starry.device.file.view.dialog.ProgressDialog
import com.czur.starry.device.file.view.dialog.RenameDialog
import com.czur.starry.device.file.view.dialog.showHintDialog
import com.czur.starry.device.file.view.vm.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by 陈丰尧 on 2022/8/9
 * 本地会议录像页面
 */
class AITransListFragment : CZViewBindingFragment<FragmentFilePadAiTransBinding>(), IFilePad,
    RefreshAble {
    companion object {
        private const val TAG = "AITransListFragment"
        private const val ONE_PAGE_SIZE = 50
    }

    private val loadingDialog by lazy {
        LoadingDialog().apply {
            showAtLeast = Lifecycle.State.STARTED
        }
    }
    private var progressDialog: ProgressDialog? = null

    // 记录是否正在刷新页面
    private val refreshing: AtomicBoolean = AtomicBoolean(false)

    private lateinit var controlBar: IControlBar

    override var copyJob: Job? = null
    override fun getBelongTo(): AccessType = AccessType.AI_TRANS

    private val access = AITransAccess

    private val adapter = AiTransListAdapter()
    private var isSelMode: Boolean = false  // 是否是选择模式

    private val isDeleting = AtomicBoolean(false)

    private val netStatusUtil = NetStatusUtil(CZURAtyManager.appContext)

    private var isNeedNetLoading: Boolean = true

    private var currentPageNo = 1
    private var isLoadingMore = false
    private var noMoreData = false // 最后一次请求少于50条数据,则不再上拉加载了

    private var downloadJob: Job? = null

    private val loginFlow = UserHandler.isLoginLive.asFlow()

    val uiStateFlow =
        combine(netStatusUtil.internetStatusLive.asFlow(), loginFlow) { netStatus, isLogin ->
            val netErrorValue = when (netStatus) {
                InternetStatus.DISCONNECT -> 1
                InternetStatus.CONNECT -> 0
            }
            val loginValue = if (isLogin) 0 else 1
            val uiValue = netErrorValue shl 1 or loginValue
            uiValue
        }.distinctUntilChanged()


    // 刷新列表中状态为未生成的选项
    private val refreshNotBeenGeneratedJob: Job by lazy {
        launch {
            while (true) {
                delay(10000)
                val list = mutableListOf<Int>()
                adapter.getDataList().forEach {
                    if (it.status == GENERATING_IN_PROGRESS) {
                        list.add(it.id)
                    }
                }
                if (list.isNotEmpty()) {
                    val entityList = access.checkTransferRecord(list)
                    if (entityList.isNotEmpty()) {
                        refreshGeneratedData(entityList)
                        refresh(false)
                    }
                }
            }
        }
    }

    private val targetFileObserver: TargetFileObserver by lazy {
        createRootFileWatch()
    }

    private val mainViewModel: MainViewModel by activityViewModels()

    // 如果有记录生成完毕了,刷新列表
    private fun refreshGeneratedData(entityList: List<AITransFileEntity>) {
        // 深拷贝adapter中的数据
        val list = adapter.getDataList().toMutableList()
        val deepCopyList = list.map { it.safeCopy() }.toMutableList()

        // 寻找entityList中,id与adapter 中id相同的item, 并更新数据
        entityList.forEach {
            val pos = deepCopyList.indexOfFirst { entity ->
                entity.id == it.id
            }
            deepCopyList[pos] = it
        }

        adapter.setData(deepCopyList)
    }


    override fun FragmentParams.initFragmentParams() {
        this.viewLifecycleObserver = object : AutoRemoveLifecycleObserver {

            override fun onStart(owner: LifecycleOwner) {
                super.onStart(owner)
                netStatusUtil.startWatching()
                targetFileObserver.startWatch()
            }

            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                netStatusUtil.stopWatching()
                targetFileObserver.stopWatch()
            }

            override fun onResume(owner: LifecycleOwner) {
                super.onResume(owner)
                logTagD(TAG, "LocalMeetingListFragment - onResume")
                if (isVisible) {
                    isNeedNetLoading = true
                    controlBar.resetSelMode()
                    refresh()   // 页面可见才去刷新
                }
            }

        }
    }


    override fun FragmentFilePadAiTransBinding.initBindingViews() {

        filePadRv.layoutManager = LinearLayoutManager(requireContext())
        filePadRv.adapter = adapter
        filePadRv.closeDefChangeAnimations()
        filePadRv.setEmptyView()

        filePadRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val lastVisibleItem = layoutManager.findLastVisibleItemPosition()
                val totalItemCount = layoutManager.itemCount

                if (!noMoreData && !isLoadingMore && lastVisibleItem == totalItemCount - 1 && dy > 0) {
                    loadMore()
                }
            }


        })

        // 点击事件
        filePadRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            fun downloadAndOpen() {
                launch {
                    logTagD(TAG, "downloadAndOpen:$pos")
                    val list = adapter.getDataList().map { it.safeCopy() }.toMutableList()
                    val aiTransFileEntity = list[pos]
                    if (aiTransFileEntity.status == 1) {
                        // 生成中
                        logTagW(TAG, "文件正在生成中,请稍后再试")
                        return@launch
                    }
                    val list1 = mutableListOf<AITransFileEntity>()
                    aiTransFileEntity.hasDownloaded = false
                    aiTransFileEntity.downloadAndOpen = true
                    list1.add(aiTransFileEntity)
                    downloadFile(list1)
                }
            }
            when (view.id) {
                R.id.open_ai_trans_record_iv -> downloadAndOpen()

                R.id.share_ai_trans_record_iv -> {
                    val bundle = Bundle()
                    val aiTransFileEntity = adapter.getDataList()[pos]
                    bundle.putString("id", aiTransFileEntity.id.toString())
                    bundle.putBoolean("onlyAsr", aiTransFileEntity.summaryPath.isNullOrEmpty())
                    val float = AITransShareQRCodeFloat()
                    float.arguments = bundle
                    float.show()
                }

                else -> {
                    isSelMode
                        .yes {
                            val selectCount = adapter.changeSel(pos)
                            val canSelectCount = adapter.getCanSelectCount()
                            controlBar.updateSelectCount(selectCount,canSelectCount)
                        }.otherwise {
                            downloadAndOpen()
                        }
                }
            }
            true
        }

        // 长按事件
        filePadRv.doOnItemLongClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            if (pos < 0) {
                return@doOnItemLongClick true
            }

            // 进入选择模式并选中当前项
            if (!isSelMode) {
                controlBar.enterSelMode()
                val selectCount = adapter.changeSel(pos)
                val canSelectCount = adapter.getCanSelectCount()
                controlBar.updateSelectCount(selectCount, canSelectCount)
            }
            true
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        refreshNotBeenGeneratedJob.start()

        repeatCollectOnResume(binding.filePadRv.isEmptyFlow) {
            controlBar.onEmptyDataChanged(it)
        }

        repeatCollectOnResume(uiStateFlow) { state ->
            logTagD(TAG, "UIstate:${state}")
            if (state >= (1 shl 1)) {
                // 无网络
                progressDialog?.dismissAllowingStateLoss()
                progressDialog = null
                downloadJob?.cancel()
            } else if (state >= 1) {
                // 未登录
                progressDialog?.dismissAllowingStateLoss()
                progressDialog = null
                downloadJob?.cancel()
            } else {
                // 正常
            }
        }
    }

    /**
     * 调整排序方式
     */
    override fun sortBy(sortType: FileAccess.SortType) {
        isNeedNetLoading = true
        refresh(true)
    }

    override fun setControlBar(iControlBar: IControlBar) {
        this.controlBar = iControlBar
    }

    /**
     * 切换选择模式
     */
    override fun changeUIMode(selectMode: Boolean) {
        this.isSelMode = selectMode
        adapter.selectMode = selectMode
//        if (!isSelMode) {
        adapter.selectNone()
//        }
    }

    fun getControlBar(): IControlBar {
        return controlBar
    }

    /**
     * 全选
     */
    override fun selectAll() {
        val selectCount = adapter.selectAll()
        val canSelectCount = adapter.getCanSelectCount()
        controlBar.updateSelectCount(selectCount, canSelectCount)
    }

    /**
     * 全不选
     */
    override fun selectNone() {
        val selectCount = adapter.selectNone()
        val canSelectCount = adapter.getCanSelectCount()
        controlBar.updateSelectCount(selectCount, canSelectCount)
    }

    /**
     * 下载文件到本地可存文件夹,然后选择目标文件夹进行复制
     */
    override fun downloadFile(targetList: List<AITransFileEntity>?) {
        downloadJob?.cancel()
        downloadJob = launch {
            val list = targetList ?: adapter.getSelFiles().map { it.safeCopy() }.toMutableList()
            // 显示进度对话框
            val progressDialogBuilder = ProgressDialog.Builder()
            progressDialogBuilder.setTitle(R.string.float_share_ai_trans_is_downloading)
            progressDialog = progressDialogBuilder
                .setOnCancelListener {
                    logTagD(TAG, "取消下载ai互译字幕协程")
                    downloadJob?.cancel()
                }
                .buildAndShow()

            access.startDownload(list) { progress, max ->
                if (progress < 0 || max < 0) {
                    logTagW(TAG, "下载失败")
                    progressDialog?.dismissAllowingStateLoss()
                    toastFail()
                } else if (progress == max) {
                    logTagD(TAG, "下载完成")
                    progressDialog?.progress = 1000
                    progressDialog?.dismissAllowingStateLoss()
                    progressDialog = null
                    toast(R.string.toast_share_ai_download_finish)
                } else {
                    logTagD(TAG, "下载进度:${progress} max:${max}")
                    progressDialog?.progress = ((progress.toFloat() / max.toFloat()) * 1000).toInt()
                }
            }

            val openFile = list.firstOrNull {
                it.downloadAndOpen && it.localAbsPath.isNotEmpty()
            }
            if (openFile != null) {
                val file = File(openFile.localAbsPath)
                val fileEntity = FileEntity(file)
                fileEntity.open(listOf(fileEntity))
            } else {
                logTagD(TAG, "没有需要打开的文件")
            }
        }
    }

    /**
     * 删除选中文件
     */
    override suspend fun deleteSelect() {
        // 显示Loading
        loadingDialog.showDelay()
        isDeleting.set(true)

        val selFiles = adapter.getSelFiles()
        selFiles.isEmpty()
            .yes {
                // 没有选中任何文件
                toast(R.string.toast_choose_no_file)
            }.otherwise {
                val res = access.delAITransFile(selFiles)
                // 执行删除操作
                when (res.first) {
                    true -> {
                        logTagV(TAG, "删除成功")
                        isDeleting.set(false)
                        isNeedNetLoading = true
                        refresh()
                    }

                    else -> {
                        if (res.second == ResCode.RESULT_CODE_NO_INTERNET
                            || res.second == ResCode.RESULT_CODE_NO_NET_CONNECT
                        ) {
                            showErrorDialog(FileAccess.Result.NO_NET_WORK)
                        }else{
                            showErrorDialog(FileAccess.Result.SYSTEM_ERR)
                        }
                        logTagD(TAG, "删除文件失败${res}")
                        isDeleting.set(false)
                        // 取消loading
                        loadingDialog.dismissImmediate()
                    }
                }
            }

    }

    override fun renameSelect() {
        super.renameSelect()
        val selFile = adapter.getSelFiles().first()
        RenameDialog(
            hint = getString(R.string.dialog_rename_hint),
            inputText = selFile.name.substringBeforeLast("."),
            title = getString(R.string.dialog_rename_title),
            isConstraintLength = true,
            limitLength = 50,   // 限制50个字符(22.9.6 刘志立)
        ) { newName ->
            when {
                newName == selFile.name.substringBeforeLast(".") -> {
                    logTagI(TAG, "文件名没有任何改变")
                }

                newName.isNotBlank() -> {
                    // 执行重命名
                    launch {
                        doRename(newName, selFile)
                    }
                }

                else -> {
                    // 没有输入文件名对话框
                    showHintDialog(getString(R.string.str_info_no_file_name))
                }
            }
        }.show()
    }

    /**
     * 执行重命名操作
     */
    private suspend fun doRename(newName: String, selFile: AITransFileEntity) {
        logTagV(TAG, "重命名,newName:${newName}")

        if (selFile.name.isEmpty() || selFile.name.isBlank()) {
            return
        }

        if (newName == selFile.name) {
            showErrorDialog(FileAccess.Result.FILE_NAME_REPETITION)
            return
        }
        loadingDialog.showDelay()

        val result = access.renameTransFile(selFile, newName)
        if (result) {
            // 重新更新UI
            logTagV(TAG, "重命名成功, 刷新")
            isNeedNetLoading = true
            refresh()
        } else {
            logTagW(TAG, "重命名失败")
            showErrorDialog(FileAccess.Result.SYSTEM_ERR)
            loadingDialog.dismissImmediate()
        }

    }

    private fun showErrorDialog(error: FileAccess.Result) {
        when (error) {
            FileAccess.Result.FILE_NAME_REPETITION ->
                // 文件名重复对话框
                showHintDialog(getString(R.string.str_info_filename_repeat))

            FileAccess.Result.SYSTEM_ERR ->
                showHintDialog(getString(R.string.str_info_system_err))

            FileAccess.Result.NO_NET_WORK ->
                showHintDialog(getString(R.string.ai_trans_no_network))

            else -> {}
        }
    }

    private fun loadMore() {
        isLoadingMore = true
        isNeedNetLoading = true
        currentPageNo += 1
        logTagI(TAG, "加载更多 currentPageNo${currentPageNo}")
        refresh(false)
        changeUIMode(false)
        controlBar.resetSelMode()
    }
    public fun refresh( needMoveToTop: Boolean,needRefreshNetData : Boolean = false) {
        isNeedNetLoading = needRefreshNetData
        refresh(needMoveToTop)
    }

    /**
     * 刷新
     */
    override fun refresh( needMoveToTop: Boolean) {
        FileHandlerLive.fileTabUpdate = mainViewModel.currentTabKey

        if (refreshing.get()) {
            logTagW(TAG, "正在刷新,ignore")
            return
        }
        if (isDeleting.get()) {
            logTagW(TAG, "正在删除,ignore")
            return
        }

//        logTagV(TAG, "刷新本地会议路径")
        launch {
            refreshing.set(true)

            if (!isLoadingMore) {
                currentPageNo = 1
            }

            if (isNeedNetLoading) {
                loadingDialog.showDelay()
                val sortType = FileShowInfoManager.loadSortType(AccessType.AI_TRANS)
                var isErrorData = false
                var dataList = withContext(Dispatchers.IO) {
                    access.getAITransFileList(currentPageNo, sortType) {
                        launch(Dispatchers.Main) {
                            isErrorData= true
                            // 当网络请求失败时显示ErrorNetworkFragment
                            val parentFragment = parentFragment
                            if (parentFragment is AITransRecordContentFragment) {
                                parentFragment.showErrorNetworkFragment()
                            }
                        }
                    }
                }

                if (isErrorData){// 接口报错,清空数据
                    noMoreData = false
                    isLoadingMore = false
                }else{
                    noMoreData = dataList.size < ONE_PAGE_SIZE
                }

                if (isLoadingMore) {
                    val list = mutableListOf<AITransFileEntity>()
                    list.addAll(adapter.getDataList().toMutableList())
                    list.addAll(dataList)

                    dataList = list
                }

                adapter.setData(dataList)
                if (!isLoadingMore && dataList.isNotEmpty()) {
                    binding.filePadRv.scrollToPosition(0)
                }
                loadingDialog.dismiss()
            }


            isNeedNetLoading = false
            isLoadingMore = false
            refreshing.set(false)
        }
    }


    private fun createRootFileWatch(): TargetFileObserver {
        return TargetFileObserver(
            access.getRootEntity().absPath,
            FileObserver.DELETE or FileObserver.CLOSE_WRITE or FileObserver.ATTRIB
        ) { event, path ->
            logTagV(TAG, "文件发生变化, event:${event}, path:${path}")
        }
    }

    override fun onDestroy() {
        refreshNotBeenGeneratedJob.cancel()
        super.onDestroy()
    }

}