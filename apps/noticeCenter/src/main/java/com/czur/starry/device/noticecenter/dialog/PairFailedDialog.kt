package com.czur.starry.device.noticecenter.dialog

import android.os.Bundle
import android.view.Gravity
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.noticecenter.databinding.ActivityPairFailedDialogBinding
import kotlinx.coroutines.delay

class PairFailedDialog : CZViewBindingAty<ActivityPairFailedDialogBinding>() {
    companion object {
        private const val TAG = "PairFailedDialog"
        private const val REQ_TIME_OUT = 20 * ONE_SECOND
    }

    override fun initWindow() {
        super.initWindow()
        if (Constants.starryHWInfo.hasTouchScreen) {
            logTagV(TAG, "触控屏,添加偏移量")
            window.apply {
                val params = attributes
                params.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }

    override fun ActivityPairFailedDialogBinding.initBindingViews() {
        setFinishOnTouchOutside(false)  // 禁止点击空白部分退出

        singleBtnFloatConfirmBtn.setOnClickListener {
            finish()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        launch {
            delay(REQ_TIME_OUT)
            logTagD(TAG, "20秒后自动取消匹配失败弹出")
            binding.singleBtnFloatConfirmBtn.performClick()
        }
    }
}