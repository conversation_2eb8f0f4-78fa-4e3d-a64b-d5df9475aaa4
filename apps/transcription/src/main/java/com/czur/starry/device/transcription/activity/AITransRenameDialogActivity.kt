package com.czur.starry.device.transcription.activity

import android.content.Intent
import android.os.Build
import android.os.SystemClock
import android.view.Gravity
import android.view.KeyEvent
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import androidx.core.view.isVisible
import androidx.core.widget.doOnTextChanged
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.addEmojiFilter
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.widget.showTextLength
import com.czur.starry.device.transcription.AITransServer
import com.czur.starry.device.transcription.R
import com.czur.starry.device.transcription.databinding.DialogRenameBinding
import com.czur.starry.device.transcription.util.createAITransFakeFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Locale

class AITransRenameDialogActivity : CZViewBindingAty<DialogRenameBinding>() {
    companion object {
        const val TAG = "AITransRenameDialogActivity"
    }
    private val transferServer: AITransServer by lazy { HttpManager.getService() }

    private var sessionId: String = ""
    private var savedFileName: String = ""
    private var startTime = 0L

    override fun initWindow() {
        super.initWindow()
        startTime = SystemClock.elapsedRealtime()
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus){
            logTagV(TAG, "适配StudioSPlus")
            window?.apply {
                val params = attributes
                params?.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logTagI(TAG,
            "重命名窗口onNewIntent savedFileName${
                intent.getStringExtra("savedFileName").orEmpty()
            }"
        )
        handlePreIntent(intent)

        logTagI(TAG, "重命名窗口onNewIntent savedFileName$savedFileName")

    }

    override fun handlePreIntent(preIntent: Intent) {
        logIntent(preIntent, TAG)
        sessionId = preIntent.getStringExtra("sessionId").orEmpty()
        // 获取从 SubtitleTransAlertWindowService 传来的默认文件名
        savedFileName = preIntent.getStringExtra("defaultFileName").orEmpty()
        logTagI(
            TAG,
            "sessionId--$sessionId, defaultFileName--$savedFileName"
        )
    }


    override fun DialogRenameBinding.initBindingViews() {
        logTagI(TAG, "打开重命名窗口")
        setFinishOnTouchOutside(false)  // 禁止点击空白部分退出
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {   //对于Android 8.0及以上
            window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
        } else {
            window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }

        // 如果没有传入默认文件名，则使用当前时间
        if (savedFileName.isEmpty()) {
            val sdf = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
            savedFileName = sdf.format(System.currentTimeMillis())
            logTagI(TAG, "使用当前时间作为默认文件名: $savedFileName")
        } else {
            logTagI(TAG, "使用传入的默认文件名: $savedFileName")
        }

        // 先在子线程中调用重命名接口
        launch {
            // 在UI线程中设置输入框文本
            withContext(Dispatchers.Main) {
                inputDialogEt.apply {
                    setText(savedFileName)
                    setSelection(savedFileName.length)

                    doOnTextChanged { text, _, _, _ ->
                        refreshConfirmEnable(text?.toString())
                    }

                    addEmojiFilter()
                    // 字符长度为50, 中文2个字符
                    showTextLength = 50

                    setOnEditorActionListener { _, actionId, event ->
                        if (actionId == EditorInfo.IME_ACTION_DONE  // 输入法软键盘的发送按钮
                            || (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) // 键盘回车键
                        ) {
                            if (confirmBtn.isEnabled && confirmBtn.isVisible) {
                                confirmBtn.performClick()
                            }
                        }
                        true
                    }
                }
            }

            if (sessionId.isNotEmpty()) {
                logTagI(TAG, "自动调用重命名接口，将文件名重命名为: $savedFileName")
                val renameResult = renameFile(sessionId, savedFileName)
                if (renameResult) {
                    logTagI(TAG, "自动重命名成功")
                } else {
                    logTagI(TAG, "自动重命名失败")
                }
            }
        }

        var isRequesting = false
        confirmBtn.setOnDebounceClickListener {
            if (isRequesting){
                return@setOnDebounceClickListener
            }
            isRequesting = true
            logTagI(TAG, "用户重命名sessionId $sessionId")
            launch {
                val inputText = inputDialogEt.text.toString()
                val renameFileResult = renameFile(sessionId, inputText)
                if (renameFileResult){
                    launch {
                        toast(R.string.toast_ai_rename_success)
                        createAITransFakeFile(inputText)
                        delay(300)
                        isRequesting = false
                        <EMAIL>()
                    }
                }else{
                    isRequesting = false
                    toast(R.string.toast_ai_rename_failed)
                }
            }
        }

        cancelBtn.setOnClickListener {
            logTagI(TAG, "用户取消重命名")
            finish()
        }

    }


    /**
     * 重命名
     */
    private suspend fun renameFile(id: String, newName: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val postRename = transferServer.postRenameRecord(id, newName)
                postRename.isSuccess
            } catch (e: Exception) {
                false
            }
        }
    }

    private fun showToast(toastFileName: String) {
        toast(getString(R.string.toast_video_file_saved, toastFileName))
    }


    /**
     * 刷新确定按钮 是否可点击
     * 如果 输入框中没有输入内容,则确定按钮不可点击
     */
    private fun DialogRenameBinding.refreshConfirmEnable(inputText: String?) {
        confirmBtn.isEnabled = !inputText.isNullOrBlank()
    }

    override fun finish() {
        super.finish()
        startTime -= 1000 // 减去1秒，避免误触发
        logTagI(TAG, "重命名窗口finish")
    }

    override fun onDestroy() {
        super.onDestroy()
        logTagI(TAG, "重命名窗口onDestroy")
        if (SystemClock.elapsedRealtime() - startTime < 1000) {
            logTagW(TAG, "重命名窗口onDestroy过快，可能是误触发，重新启动")
            startActivity(intent)
            return
        }
    }
}