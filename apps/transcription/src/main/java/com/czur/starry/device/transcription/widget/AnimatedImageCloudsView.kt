package com.czur.starry.device.transcription.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Handler
import android.util.AttributeSet
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.core.content.ContextCompat
import com.czur.starry.device.baselib.utils.ONE_SECOND

class AnimatedImageCloudsView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val handler = Handler()
    private var currentImageIndex = 0
    private val loopStart = 0
    private val loopEnd = 249
    private val packageName = context.packageName
    private var scaleYFactorStart: Float = 0.5f
    private var scaleYFactorEnd: Float = 0.5f
    private var interval: Long = 900L
    private var isQ2Series: Boolean = false

    private fun updateImage() {
//        logTagD(TAG, "======START======updateImage")
        val resourceName =
//            if (isQ2Series) {
//            "q2_clouds_%05d".format(currentImageIndex)
//        }else {
            "clouds_%05d".format(currentImageIndex)
//        }
        val resourceId = context.resources.getIdentifier(resourceName, "drawable", packageName)
        val drawable: Drawable? = ContextCompat.getDrawable(context, resourceId)
        drawable?.let {
            background = it
        }
        // 更新索引
        if (currentImageIndex < loopEnd) {
            currentImageIndex++
        } else {
            // 循环到末尾，回到循环起始点
            currentImageIndex = loopStart
        }

    }

    private fun startAnimation(interval: Long = 30L) {
        visibility = VISIBLE
        handler.postDelayed({
            updateImage()
            startAnimation(interval) // 递归调用保持动画
        }, interval)

    }


    fun initAnimation(isQ2Series: Boolean = false) {
        this.isQ2Series = isQ2Series
        visibility = VISIBLE
        updateImage()
        scaleXYAnimation(true)
    }
    private var currentAnimator: Animator? = null
    fun setScaleYFactor(start: Float = scaleYFactorStart, end: Float = scaleYFactorEnd) {
        currentAnimator?.cancel() // 取消之前的动画

        val anim = ObjectAnimator.ofFloat(this, "scaleY", start, end)
        anim.duration = ONE_SECOND
        anim.repeatMode = ObjectAnimator.REVERSE
        anim.repeatCount = ObjectAnimator.INFINITE
        anim.start()
        currentAnimator = anim
    }
    fun triggerWaveEffect(value: Float) {
        currentAnimator?.cancel()

        // 波动动画：从当前 scaleY 值放大到 value，再返回
        val waveAnim = ObjectAnimator.ofFloat(this, "scaleY", scaleY, value)
        waveAnim.duration = interval
        waveAnim.repeatMode = ObjectAnimator.REVERSE
        waveAnim.repeatCount = 1 // 播放两次（正向 + 反向）

        waveAnim.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                // 波动结束后，启动过渡动画回到 scaleYFactorStart
                val transitionAnim = ObjectAnimator.ofFloat(this, "scaleY", scaleY, scaleYFactorStart)
                transitionAnim.duration = interval
                transitionAnim.interpolator = AccelerateDecelerateInterpolator()

                transitionAnim.addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        // 过渡结束后启动默认动画
                        setScaleYFactor(scaleYFactorStart, scaleYFactorEnd)
                    }
                })

                transitionAnim.start()
                currentAnimator = transitionAnim
            }
        })

        waveAnim.start()
        currentAnimator = waveAnim
    }

    fun stopAnimation() {
        handler.removeCallbacksAndMessages(null)
        scaleXYAnimation(false)
    }

    private fun scaleXYAnimation(isShow: Boolean = false) {
        lateinit var scaleXAnimator: ObjectAnimator
        lateinit var scaleYAnimator: ObjectAnimator
        if (isShow) {
            scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 0f, 1f)
            scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0f, 1f)
        } else {
            scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 0f)
            scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0f)
        }
        // 设置动画的持续时长为500毫秒
        scaleXAnimator.duration = 500
        scaleYAnimator.duration = 500
        // 为动画添加监听器，以便在动画结束时隐藏视图
        val animatorListener = object : Animator.AnimatorListener {
            override fun onAnimationStart(p0: Animator) {
            }

            override fun onAnimationEnd(p0: Animator) {
                if (!isShow) {
                    visibility = GONE
                } else {
                    startAnimation()
                }
            }

            override fun onAnimationCancel(p0: Animator) {
            }

            override fun onAnimationRepeat(p0: Animator) {
            }

        }

        // 为缩放动画设置监听器
        scaleXAnimator.addListener(animatorListener)
        scaleYAnimator.addListener(animatorListener)

        scaleXAnimator.start()
        scaleYAnimator.start()
    }


    // 重写onDetachedFromWindow以在View被销毁时停止动画
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopAnimation()
    }
}