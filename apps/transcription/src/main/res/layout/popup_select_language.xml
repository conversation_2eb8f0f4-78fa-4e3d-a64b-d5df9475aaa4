<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/selectLanguageLl"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/shape_white_r_10"
    tools:ignore="PxUsage" >

    <FrameLayout
        android:layout_width="212px"
        android:layout_height="308px"
        android:layout_marginTop="10px"
        android:layout_marginBottom="10px"
        android:layout_gravity="left">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/language_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="vertical"
            android:scrollbarThumbVertical="@drawable/custom_scrollbar_thumb"
            android:scrollbarSize="4px"
            android:scrollbarFadeDuration="0"
            android:scrollbarStyle="outsideOverlay"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/loading_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <ProgressBar
                style="@style/Widget.AppCompat.ProgressBar"
                android:layout_width="60px"
                android:layout_height="60px"
                android:layout_gravity="center"
                android:indeterminateTint="#5879FC"
                android:indeterminateTintMode="src_atop" />

        </FrameLayout>
    </FrameLayout>

</LinearLayout>

