package com.czur.starry.device.settings.ui.personalization.assistant

import android.content.Intent
import android.os.Bundle
import com.czur.czurutils.global.globalAppCtx
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_VOICE_ASSISTANT
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantEnabled
import com.czur.starry.device.baselib.utils.fw.proxy.AudioAIManagerProxy
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentAiAssistantBinding

/**
 * Created by 陈丰尧 on 2025/5/9
 */
class AIAssistantFragment : BaseBindingMenuFragment<FragmentAiAssistantBinding>() {
    private val audioAIManager: AudioAIManagerProxy by lazy {
        AudioAIManagerProxy()
    }

    override fun FragmentAiAssistantBinding.initBindingViews() {

        binding.totalSwitch.setOnSwitchChangeListener { isOn: Boolean, fromUser: Boolean ->
            if (fromUser) {
                audioAIManager.setInteractionStatus(isOn)
                binding.voiceWakeupSwitch.setGrayMode(isOn)
                binding.onlyTextSwitch.setGrayMode(isOn)
                isVoiceAssistantEnabled = isOn
                noticeAIServiceEvent()
            }
        }

        binding.voiceWakeupSwitch.setOnSwitchChangeListener { isOn: Boolean, fromUser: Boolean ->
            if (fromUser) {
                audioAIManager.setInteractionStageStatus(isOn)
            }
        }
        binding.onlyTextSwitch.setOnSwitchChangeListener { isOn: Boolean, fromUser: Boolean ->
            if (fromUser) {
                audioAIManager.setTtsStatus(isOn)
            }

        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        val totalEnable = audioAIManager.getInteractionStatus()
        binding.totalSwitch.setSwitchOn(totalEnable)
        isVoiceAssistantEnabled  = totalEnable
        val enable = audioAIManager.getInteractionStageStatus()
        binding.voiceWakeupSwitch.setSwitchOn(enable)
        val ttsEnable = audioAIManager.getTtsStatus()
        binding.onlyTextSwitch.setSwitchOn(ttsEnable)

        binding.voiceWakeupSwitch.setGrayMode(totalEnable)
        binding.onlyTextSwitch.setGrayMode(totalEnable)
    }

    // 通知小星处理事件
    private fun noticeAIServiceEvent() {
        val intent = Intent().apply {
            `package` = "com.czur.starry.device.voiceassistant"
            action = ACTION_BOOT_VOICE_ASSISTANT
            putExtra("key_message", "wake_up_switch")
        }
        globalAppCtx.startService(intent)
    }

}