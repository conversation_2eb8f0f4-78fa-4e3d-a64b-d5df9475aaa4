package com.czur.starry.device.voiceassistant.helper

import android.content.Context
import android.view.KeyEvent
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy.USBModeState
import com.czur.starry.device.baselib.view.floating.DisplayPeripheralModeType
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.systemTrackModeStatus
import com.czur.starry.device.voiceassistant.R
import com.czur.starry.device.voiceassistant.R.string
import com.czur.starry.device.voiceassistant.util.EShareUtil.setAirplayEnable
import com.czur.starry.device.voiceassistant.util.EShareUtil.setDLNAEnable
import com.czur.starry.device.voiceassistant.util.EShareUtil.setDeviceNameShow
import com.czur.starry.device.voiceassistant.util.EShareUtil.setHdmiMuxStatus
import com.czur.starry.device.voiceassistant.util.EShareUtil.setMiracastEnable
import com.czur.starry.device.voiceassistant.util.EShareUtil.setMultiScreen
import com.czur.starry.device.voiceassistant.util.EShareUtil.setScreenShareAsk
import com.czur.starry.device.voiceassistant.util.EShareUtil.setSmartFullScreen
import com.czur.starry.device.voiceassistant.util.FileSetUtil.playVideo
import com.czur.starry.device.voiceassistant.util.FileSetUtil.setFileTransfer
import com.czur.starry.device.voiceassistant.util.FileSetUtil.setFileTransferCode
import com.czur.starry.device.voiceassistant.util.FileSetUtil.setFileTransferDownload
import com.czur.starry.device.voiceassistant.util.FileSetUtil.setTranscription
import com.czur.starry.device.voiceassistant.util.FileSetUtil.stopAudioRecording
import com.czur.starry.device.voiceassistant.util.FileSetUtil.stopScreenRecording
import com.czur.starry.device.voiceassistant.util.FileSetUtil.stopVideo
import com.czur.starry.device.voiceassistant.util.FileSetUtil.stopVideoRecording
import com.czur.starry.device.voiceassistant.util.FileSetUtil.updateFileSort
import com.czur.starry.device.voiceassistant.util.FileSetUtil.updatePicSize
import com.czur.starry.device.voiceassistant.util.FileSetUtil.videoFastBackward
import com.czur.starry.device.voiceassistant.util.FileSetUtil.videoFastForward
import com.czur.starry.device.voiceassistant.util.SettingsUtil.doAutoFocus
import com.czur.starry.device.voiceassistant.util.SettingsUtil.setAutoFocusStatus
import com.czur.starry.device.voiceassistant.util.SettingsUtil.setAutoKeystoneStatus
import com.czur.starry.device.voiceassistant.util.SettingsUtil.setWifiStatus
import com.czur.starry.device.voiceassistant.util.SettingsUtil.updateAGCLevel
import com.czur.starry.device.voiceassistant.util.SettingsUtil.updateCameraMode
import com.czur.starry.device.voiceassistant.util.SettingsUtil.updateDisplayMode
import com.czur.starry.device.voiceassistant.util.SettingsUtil.updateNSLevel
import com.czur.starry.device.voiceassistant.util.SettingsUtil.updateScreenBrightness
import com.czur.starry.device.voiceassistant.util.SettingsUtil.updateScreenSize
import com.czur.starry.device.voiceassistant.util.SystemUtil.goBackHome
import com.czur.starry.device.voiceassistant.util.SystemUtil.mediaMute
import com.czur.starry.device.voiceassistant.util.SystemUtil.onScreenOff
import com.czur.starry.device.voiceassistant.util.SystemUtil.onScreenShort
import com.czur.starry.device.voiceassistant.util.SystemUtil.pageUpDown
import com.czur.starry.device.voiceassistant.util.SystemUtil.simulateLongPressPowerKey
import com.czur.starry.device.voiceassistant.util.SystemUtil.updateVolume
import com.eshare.serverlibrary.api.EShareServerSDK

/**
 *  author : WangHao
 *  time   :2025/02/12
 */


enum class SettingType {
    RECORDING,         //拾音
    NOISE_SUPPRESSION, //噪声抑制
    SCREEN,            //屏幕画面
    CAMERA,            //摄像头
    OFFICE_MODE,       //办公模式
    AUTO_FOCUS,        //自动对焦
    SCREEN_OFF,        //屏幕关闭
    AUTO_KEY_STONE,      //梯形校正
    WIFI,               //WIFI
    SCREEN_BRIGHTNESS,    //屏幕亮度

    VOLUME,            //音量
    SCREEN_SHORT,       //截图
    BACK_HOME,          //返回主页
    SHUT_DOWN_REBOOT,   //关机重启
    PAGE_UP,             //上一页
    PAGE_DOWN,           //下一页


    FILE_TRANSFER,        //允许传输文件
    FILE_TRANSFER_CODE,   //传输校验码
    FILE_TRANSFER_DOWNLOAD,  //下载会议录制文件
    SETTING_FILE,            //文件
    PICTURE,               //图片
    VIDEO_PAUSE,           //视频暂停
    VIDEO_PLAY,            //视频播放
    VIDEO_FAST_FORWARD,     //视频快进
    VIDEO_FAST_BACKWARD,     //视频快退
    MEDIA_MUTE,              //静音
    AUDIO_RECORDING,        // 录音
    SCREEN_RECORDING,       // 录屏
    VIDEO_RECORDING,        // 录像
    AI_SUBTITLE_TRANSLATION, // AI字幕互译

    MIRACAST,               //Miracast
    AIRPLAY,                //Airplay
    DLNA,                   //DLNA
    MULTI_SCREEN,           //多屏同投
    HDMI_AIRPLAY,           //HDMI混投
    DEVICE_NAME_FLOATING,   //设备名悬浮窗
    SMART_FULL_SCREEN,      //智能满屏
    SCREEN_SHARE_ASK,       //投屏询问

    UNKNOWN                  //未匹配
}

enum class Adjustable {
    ADD,
    SUB,
    MAX,
    MIN,
    PERCENT
}

//设置执行返回状态
enum class SettingResult {
    SUCCESS,               // 匹配成功
    FAILED,               // 失败
    CANT_EXECUTE,         // 不能执行（比如 外设模式下不能息屏）
    CANT_EXECUTE_AI,         // 不能执行（ AI字幕模式下不能息屏）
}

object SettingAppHelper {
    const val TAG = "SettingAppHelper"

    private lateinit var context: Context
    private lateinit var stringMap: Map<String, SettingType>
    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(context)
    }
    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }

    fun init(context: Context) {
        this.context = context
        stringMap = mapOf(
            context.getString(string.setting_recording) to SettingType.RECORDING,
            context.getString(string.setting_noise_suppression) to SettingType.NOISE_SUPPRESSION,
            context.getString(string.setting_screen) to SettingType.SCREEN,
            context.getString(string.setting_camera) to SettingType.CAMERA,
            context.getString(string.setting_display_mode) to SettingType.OFFICE_MODE,
            context.getString(string.setting_auto_focus) to SettingType.AUTO_FOCUS,
            context.getString(string.setting_auto_keystone) to SettingType.AUTO_KEY_STONE,
            context.getString(string.setting_wifi) to SettingType.WIFI,
            context.getString(string.setting_screen_brightness) to SettingType.SCREEN_BRIGHTNESS,

            context.getString(string.setting_screen_volume) to SettingType.VOLUME,
            context.getString(string.setting_screen_off) to SettingType.SCREEN_OFF,
            context.getString(string.setting_screen_short) to SettingType.SCREEN_SHORT,
            context.getString(string.setting_home) to SettingType.BACK_HOME,
            context.getString(string.setting_shut_down) to SettingType.SHUT_DOWN_REBOOT,
            context.getString(string.setting_reboot) to SettingType.SHUT_DOWN_REBOOT,
            context.getString(string.setting_page_up) to SettingType.PAGE_UP,
            context.getString(string.setting_page_down) to SettingType.PAGE_DOWN,


            context.getString(string.app_name_file) to SettingType.SETTING_FILE,
            context.getString(string.setting_picture) to SettingType.PICTURE,
            context.getString(string.setting_file_transfer) to SettingType.FILE_TRANSFER,
            context.getString(string.setting_file_transfer_code) to SettingType.FILE_TRANSFER_CODE,
            context.getString(string.setting_file_transfer_download) to SettingType.FILE_TRANSFER_DOWNLOAD,
            context.getString(string.setting_video_pause) to SettingType.VIDEO_PAUSE,
            context.getString(string.setting_video_play) to SettingType.VIDEO_PLAY,
            context.getString(string.setting_video_forward) to SettingType.VIDEO_FAST_FORWARD,
            context.getString(string.setting_video_back) to SettingType.VIDEO_FAST_BACKWARD,
            context.getString(string.setting_media_mute) to SettingType.MEDIA_MUTE,
            context.getString(string.voice_command_record) to SettingType.AUDIO_RECORDING,
            context.getString(string.voice_command_screen) to SettingType.SCREEN_RECORDING,
            context.getString(string.voice_command_video) to SettingType.VIDEO_RECORDING,
            context.getString(string.setting_ai_subtitle_translate) to SettingType.AI_SUBTITLE_TRANSLATION,

            context.getString(string.screenshare_miracast) to SettingType.MIRACAST,
            context.getString(string.screenshare_airplay) to SettingType.AIRPLAY,
            context.getString(string.screenshare_dlna) to SettingType.DLNA,
            context.getString(string.screenshare_multi_screen) to SettingType.MULTI_SCREEN,
            context.getString(string.screenshare_hdmi_airplay) to SettingType.HDMI_AIRPLAY,
            context.getString(string.screenshare_name_floating) to SettingType.DEVICE_NAME_FLOATING,
            context.getString(string.screenshare_smart_full) to SettingType.SMART_FULL_SCREEN,
            context.getString(string.screenshare_ask) to SettingType.SCREEN_SHARE_ASK,
            // ... 其他映射
        )
    }

    private fun fromString(chineseString: String): SettingType? {
        return stringMap[chineseString] ?: SettingType.UNKNOWN
    }

    //设置应用的参数
    fun setAppParams(target: String, params: String, functionList: List<String>): SettingResult {
        val settingType = fromString(target)!!
        if (settingType == SettingType.UNKNOWN || isNotSupport(target, functionList)) {
            logTagE(TAG, "=====未知的target设置项:$target")
            return SettingResult.FAILED
        } else {
            return executeSetting(settingType, params)
        }
        return SettingResult.FAILED
    }

    //不支持功能
    private fun isNotSupport(target: String, functionList: List<String>): Boolean {
        return Constants.starryHWInfo.series == StudioSeries && functionList.contains(target)
    }

    /**
     * 执行
     */
    private fun executeSetting(settingType: SettingType, params: String): SettingResult {
        when (settingType) {
            SettingType.RECORDING -> {
                when (params) {
                    context.getString(string.setting_recording_high) -> updateAGCLevel(SettingUtil.CameraAndMicSetting.AudioAlgoAGCLevel.HIGH)
                    context.getString(string.setting_recording_normal) -> updateAGCLevel(SettingUtil.CameraAndMicSetting.AudioAlgoAGCLevel.NORMAL)
                }
            }

            SettingType.NOISE_SUPPRESSION -> {
                when (params) {
                    context.getString(string.setting_noise_suppression_high) -> updateNSLevel(
                        SettingUtil.CameraAndMicSetting.AudioAlgoNSLevel.HIGH
                    )

                    context.getString(string.setting_noise_suppression_medium) -> updateNSLevel(
                        SettingUtil.CameraAndMicSetting.AudioAlgoNSLevel.MID
                    )

                    context.getString(string.setting_noise_suppression_low) -> updateNSLevel(
                        SettingUtil.CameraAndMicSetting.AudioAlgoNSLevel.LOW
                    )

                    context.getString(string.setting_noise_suppression_close) -> updateNSLevel(
                        SettingUtil.CameraAndMicSetting.AudioAlgoNSLevel.NONE
                    )
                }
            }

            SettingType.SCREEN -> {
                when (params) {
                    context.getString(string.setting_params_add) -> updateScreenSize(Adjustable.ADD)
                    context.getString(string.setting_params_sub) -> updateScreenSize(Adjustable.SUB)
                    context.getString(string.setting_params_max) -> updateScreenSize(Adjustable.MAX)
                    context.getString(string.setting_params_min) -> updateScreenSize(Adjustable.MIN)
                    else -> {
                        try {
                            var value = params
                            if (params.contains("%")) {
                                value = params.replace("%", "")
                            }
                            updateScreenSize(percent = value.toInt())
                        } catch (e: Exception) {
                            logTagE(TAG, "====e=$e")
                        }
                    }
                }
            }

            SettingType.SCREEN_BRIGHTNESS -> {
                if (!Constants.starryHWInfo.hasTouchScreen) return SettingResult.FAILED
                when (params) {
                    context.getString(string.setting_params_add) -> updateScreenBrightness(
                        context,
                        Adjustable.ADD
                    )

                    context.getString(string.setting_params_sub) -> updateScreenBrightness(
                        context,
                        Adjustable.SUB
                    )

                    context.getString(string.setting_params_max) -> updateScreenBrightness(
                        context,
                        Adjustable.MAX
                    )

                    context.getString(string.setting_params_min) -> updateScreenBrightness(
                        context,
                        Adjustable.MIN
                    )
                }
            }

            SettingType.CAMERA -> {
                when (params) {
                    context.getString(string.setting_camera_complete) -> updateCameraMode(
                        SystemManagerProxy.TrackMode.TRACK_MODE_OFF
                    )

                    context.getString(string.setting_camera_smart) -> updateCameraMode(
                        SystemManagerProxy.TrackMode.TRACK_MODE_VISION
                    )
                }
            }

            SettingType.OFFICE_MODE -> {
                when (params) {
                    context.getString(string.setting_display_mode_office) -> updateDisplayMode(
                        SystemManagerProxy.LooksMode.LOOKS_MODE_8880K
                    )

                    context.getString(string.setting_display_mode_viewing) -> updateDisplayMode(
                        SystemManagerProxy.LooksMode.LOOKS_MODE_6500K
                    )
                }
            }

            SettingType.AUTO_FOCUS -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        //开启自动对焦
                        setAutoFocusStatus(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        //关闭自动对焦
                        setAutoFocusStatus(false)
                    }

                    else -> {
                        doAutoFocus()
                    }
                }

            }

            SettingType.AUTO_KEY_STONE -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        //开启自动梯形矫正
                        setAutoKeystoneStatus(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        //关闭自动梯形矫正
                        setAutoKeystoneStatus(false)
                    }
                }
            }

            SettingType.WIFI -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        //开启wifi
                        setWifiStatus(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        //关闭WIFI
                        setWifiStatus(false)
                    }
                }
            }


            SettingType.VOLUME -> {
                when (params) {
                    context.getString(string.setting_params_add) -> updateVolume(Adjustable.ADD)
                    context.getString(string.setting_params_sub) -> updateVolume(Adjustable.SUB)
                    context.getString(string.setting_params_max) -> updateVolume(Adjustable.MAX)
                    context.getString(string.setting_params_min) -> updateVolume(Adjustable.MIN)
                    else -> {
                        try {
                            logTagE(TAG, "====p=$params")
                            updateVolume(Adjustable.PERCENT, params)
                        } catch (e: Exception) {
                            logTagE(TAG, "====e=$e")
                        }
                    }
                }
            }

            SettingType.MEDIA_MUTE -> {
                mediaMute()
            }

            SettingType.SCREEN_OFF -> {
                //判断是否是外设模式
                val isByomrunning = eShareServerSDK.isBYOMRunning
                val usbMode = systemManager.getGadgetMode() == USBModeState.USB_GADGET_STREAM_ON
                if (isByomrunning || usbMode) {
                    return SettingResult.CANT_EXECUTE
                }
                if (TransHandler.isTranslating) {
                    return SettingResult.CANT_EXECUTE_AI
                }
                onScreenOff()
            }

            SettingType.SCREEN_SHORT -> {
                onScreenShort()
            }

            SettingType.BACK_HOME -> {
                goBackHome()
            }

            SettingType.SHUT_DOWN_REBOOT -> {
                simulateLongPressPowerKey()
            }

            SettingType.PAGE_UP -> {
                pageUpDown(KeyEvent.KEYCODE_PAGE_UP)
            }

            SettingType.PAGE_DOWN -> {
                pageUpDown(KeyEvent.KEYCODE_PAGE_DOWN)
            }

            SettingType.SETTING_FILE -> {
                if (params == context.getString(string.setting_file_sort_type)) {
                    updateFileSort()
                }

            }

            SettingType.PICTURE -> {
                when (params) {
                    context.getString(string.setting_params_add) -> updatePicSize(Adjustable.ADD)
                    context.getString(string.setting_params_sub) -> updatePicSize(Adjustable.SUB)
                }
            }

            SettingType.FILE_TRANSFER -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        //开启文件传输
                        setFileTransfer(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        setFileTransfer(false)
                    }
                }
            }

            SettingType.FILE_TRANSFER_CODE -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        setFileTransferCode(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        setFileTransferCode(false)
                    }
                }
            }

            SettingType.FILE_TRANSFER_DOWNLOAD -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        setFileTransferDownload(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        setFileTransferDownload(false)
                    }
                }
            }

            SettingType.VIDEO_PAUSE -> {
                stopVideo()
            }

            SettingType.VIDEO_PLAY -> {
                playVideo()
            }

            SettingType.VIDEO_FAST_FORWARD -> {
                videoFastForward()
            }

            SettingType.VIDEO_FAST_BACKWARD -> {
                videoFastBackward()
            }

            SettingType.AUDIO_RECORDING -> {
                stopAudioRecording()
            }

            SettingType.SCREEN_RECORDING -> {
                stopScreenRecording()
            }

            SettingType.VIDEO_RECORDING -> {
                stopVideoRecording()
            }

            SettingType.AI_SUBTITLE_TRANSLATION -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        if (!TransHandler.isTranslating) {
                            setTranscription(true)
                        }

                    }

                    context.getString(string.setting_switch_off) -> {
                        if (TransHandler.isTranslating) {
                            TransHandler.stopTrans = true
                            TransHandler.isTranslating = false
                        }
                    }
                }

            }

            //无线投屏部分
            SettingType.MIRACAST -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        setMiracastEnable(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        setMiracastEnable(false)
                    }
                }
            }

            SettingType.AIRPLAY -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        setAirplayEnable(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        setAirplayEnable(false)
                    }
                }
            }

            SettingType.DLNA -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        setDLNAEnable(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        setDLNAEnable(false)
                    }
                }
            }

            SettingType.MULTI_SCREEN -> {
                setMultiScreen(params.toInt())
            }

            SettingType.HDMI_AIRPLAY -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        //开启HDMI混投
                        setHdmiMuxStatus(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        //关闭WIFI
                        setHdmiMuxStatus(false)
                    }
                }
            }

            SettingType.DEVICE_NAME_FLOATING -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        //开启设备名悬浮窗
                        setDeviceNameShow(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        //开启设备名悬浮窗
                        setDeviceNameShow(false)
                    }
                }
            }

            SettingType.SMART_FULL_SCREEN -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        //开启智能满屏
                        setSmartFullScreen(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        //关闭智能满屏
                        setSmartFullScreen(false)
                    }
                }
            }

            SettingType.SCREEN_SHARE_ASK -> {
                when (params) {
                    context.getString(string.setting_switch_on) -> {
                        //开启投屏询问
                        setScreenShareAsk(true)
                    }

                    context.getString(string.setting_switch_off) -> {
                        //开启投屏询问
                        setScreenShareAsk(false)
                    }
                }
            }

            else -> {
                logTagE(TAG, "=====未知的params设置参数项:$params")
                return SettingResult.FAILED
            }
        }
        return SettingResult.SUCCESS

    }


}

