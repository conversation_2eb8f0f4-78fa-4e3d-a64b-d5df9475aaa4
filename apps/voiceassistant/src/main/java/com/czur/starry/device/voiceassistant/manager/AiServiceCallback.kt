package com.czur.starry.device.voiceassistant.manager

import android.audioai.InteractionAiServiceCallback
import android.os.Build
import android.os.SystemClock
import androidx.annotation.RequiresApi
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.data.provider.StarryDataProvider.gson
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.voiceassistant.VoiceAssistantService.Companion.isWakeupState
import com.czur.starry.device.voiceassistant.common.Constants.POWER_OFF_EVENT
import com.czur.starry.device.voiceassistant.common.Constants.WAKE_UP_EVENT
import com.czur.starry.device.voiceassistant.entity.ChatMsgData
import com.czur.starry.device.voiceassistant.entity.ChatMsgNoTtsData
import com.czur.starry.device.voiceassistant.entity.CommandEvent
import com.czur.starry.device.voiceassistant.entity.CommandEventHandler
import com.czur.starry.device.voiceassistant.entity.MessageData
import com.czur.starry.device.voiceassistant.entity.SubtitleItem
import com.czur.starry.device.voiceassistant.entity.TtsMessageData
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.asrQueue
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.ttsAudioQueue
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.ttsMessageQueue
import com.czur.starry.device.voiceassistant.util.ParseDataUtil.parseAudioMetaData
import com.czur.starry.device.voiceassistant.util.ParseDataUtil.parseChatMsg
import com.czur.starry.device.voiceassistant.util.ParseDataUtil.parseChatNoTtsMsg
import com.czur.starry.device.voiceassistant.util.ParseDataUtil.parseInstruct
import com.czur.starry.device.voiceassistant.util.ParseDataUtil.parseTtsMsg
import com.czur.starry.device.voiceassistant.util.ParseDataUtil.parseWakeUpResult
import com.czur.starry.device.voiceassistant.view.WindowUI
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.lang.reflect.Modifier.isFinal
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 *  author : WangHao
 *  time   :2025/03/25
 */

private const val TAG = "AiServiceCallback"

class AiServiceCallback(
    private val lifecycleOwner: LifecycleOwner,
    private val windowUI: WindowUI,
    private val ampProcessor: AmpProcessor,
    private val eventHandler: CommandEventHandler
) : InteractionAiServiceCallback {

    private val powerOffCode = 201

    private var coolDownPeriod = 2000L // 默认2秒
    private val lastInstructTime = AtomicLong(0)
    private val isMicState = AtomicBoolean(false)

    private val audioMetaCompletionTime = AtomicLong(0) // 记录 onAudioMeta 完成的时间戳

    //唤醒id过滤一级唤醒和二级唤醒
    val wakeupId = AtomicLong(0)

    // 消息队列，用于存储待处理的消息
    private val chatMsgNoTtsQueue = mutableListOf<ChatMsgNoTtsData>()

    // 标记当前是否正在处理消息
    private val isProcessingMessages = AtomicBoolean(false)

    //喚醒
    override fun onWakeUp(meta: String?) {
        logTagD(TAG, "onWakeUp result:${meta}")
        val timestamp = parseWakeUpResult(meta)?.timestamp ?: return
        if (timestamp == wakeupId.get() && isWakeupState.get()) {
            logTagD(TAG, "过滤重复唤醒:${timestamp}")
            //当一级唤醒被使用了，二级唤醒就不使用了
            return
        }
        wakeupId.set(timestamp)
        isMicState.set(true)
        eventHandler.handleCommand(CommandEvent(WAKE_UP_EVENT))
    }

    override fun onStreamStoped(meta: String?) {
        logTagD(TAG, "onStreamStoped result:${meta}")
        isMicState.set(false)
    }

    // ASR 说话数据
    override fun onAudioMeta(metaJson: String?) {
        logTagD(TAG, "metaJson result:${metaJson}")
        isMicState.set(true)
        val audioMetaData = parseAudioMetaData(metaJson) ?: return
        wakeupId.set(audioMetaData.context_id.toLong())
        if (audioMetaData.text.isNullOrEmpty() || !isWakeupState.get()) {
            return
        }

        lifecycleOwner.launch {
            asrQueue.get().trySend(MessageData(audioMetaData.text, true, audioMetaData.asr_fin))
            if (audioMetaData.asr_fin) {
                audioMetaCompletionTime.set(SystemClock.elapsedRealtime()) // 记录完成时间
            }
        }

    }

    //指令
    override fun onInstruct(instructJson: String) {
        logTagD(TAG, "onInstruct result:$instructJson")
        if (!isWakeupState.get()) {
            logTagD(TAG, "onInstruct!isWakeupState:return")
            return
        }
        isMicState.set(false)
        val now = SystemClock.elapsedRealtime()
        if (now - lastInstructTime.get() < coolDownPeriod) {
            logTagD(TAG, "指令冷却中，忽略本次指令")
            return
        }
        if (!lastInstructTime.compareAndSet(lastInstructTime.get(), now)) {
            logTagD(TAG, "并发指令已由其他线程处理")
            return
        }
        val result = parseInstruct(instructJson)
        result?.instructionResults?.first { !it.intent.isNullOrEmpty() }?.let {
            lifecycleOwner.launch {
                // 计算 onAudioMeta 完成到现在的时间差
                val elapsedTime = SystemClock.elapsedRealtime() - audioMetaCompletionTime.get()
                if (elapsedTime < ONE_SECOND) {
                    // 若不足 500ms，等待剩余时间
                    delay(ONE_SECOND - elapsedTime)
                }
                eventHandler.handleCommand(CommandEvent(it.intent, it.target, it.param))
            }
        }
        lastInstructTime.set(SystemClock.elapsedRealtime())
    }

    //小星回复的消息
    override fun onChatMsg(msgJson: String?) {
        logTagD(TAG, "onChatMsg result:$msgJson")

        if (msgJson == null) return
        val jsonElement: JsonElement = JsonParser.parseString(msgJson)
        val contextJson = jsonElement.asJsonObject.getAsJsonObject("context")
        // 封装解析逻辑
        val chatMsgData = parseChatMessage(contextJson, msgJson) ?: return

        // 封装过滤逻辑
        if (shouldFilterMessage(chatMsgData)) {
            logTagD(TAG, "onChatMsg !isWakeupState:return or subtitles.isNullOrEmpty")
            return
        }

        isMicState.set(false)

        // 处理不同类型的消息
        when {
            chatMsgData is ChatMsgData && chatMsgData.context?.subtitles?.items?.isNotEmpty() == true -> {
                handleSubtitleItems(chatMsgData)
            }

            chatMsgData is ChatMsgNoTtsData && chatMsgData.context?.text?.isNotEmpty() == true -> {
                handleTextMessage(chatMsgData)
            }

            else -> {
                sendDefaultTtsMessage(chatMsgData)
            }
        }


    }

    //小星回复的语音
    override fun onTtS(audioData: ByteArray?, meta: String?) {
        logTagD(TAG, "onTTS audioData:$audioData,meta:$meta")
        val ttsMsg = parseTtsMsg(meta) ?: return
        if (audioData == null || !isWakeupState.get() || (wakeupId.get()
                .toString() != ttsMsg.context.context_id)
        ) {
            logTagD(TAG, "audioData !isWakeupState:return")
            return
        }
        isMicState.set(false)
        lifecycleOwner.launch {
            ttsAudioQueue.get().trySend(audioData)
        }
    }

    override fun onMicAmp(json: String?) {
//        logTagD(TAG, "onMicAmp :$json")
        if (!isWakeupState.get() || !isMicState.get()) {
            logTagD(TAG, "onMicAmp !isWakeupState:return")
            return
        }
        ampProcessor.onRawJsonData(json ?: return)
    }

    override fun onError(errorCode: Int, errorMsg: String?) {
        logTagD(TAG, " errorCode:$errorCode,errorMsg:$errorMsg")
        if (!isWakeupState.get()) {
            logTagD(TAG, "!isWakeupState:return")
            return
        }
        isMicState.set(false)
        lifecycleOwner.launch {
            if (errorCode == powerOffCode) {
                eventHandler.handleCommand(CommandEvent(POWER_OFF_EVENT))
            }
        }
    }

    override fun onFirstStageWakeUp(meta: String?) {
        logTagD(TAG, "onFirstStageWakeUp result:${meta}")
        if (getBooleanSystemProp("persist.voice.ai.toast", false)) {
            NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                put("一阶段唤醒")
            }
        }
        if (!isWakeupState.get()) {
            logTagD(TAG, "!isWakeupState:return")
            return
        }
        //只有打断的唤醒情况下 使用一级唤醒打断
        wakeupId.set(meta?.toLong() ?: return)
        eventHandler.handleCommand(CommandEvent(WAKE_UP_EVENT))
    }


    private fun parseChatMessage(contextJson: JsonObject, msgJson: String): Any? {
        return if (contextJson.has("subtitles")) {
            parseChatMsg(msgJson)
        } else if (contextJson.has("text")) {
            parseChatNoTtsMsg(msgJson)
        } else {
            null
        }
    }

    private fun shouldFilterMessage(chatMsgData: Any): Boolean {
        return when (chatMsgData) {
            is ChatMsgData -> !isWakeupState.get() || wakeupId.get()
                .toString() != chatMsgData.context?.contextId

            is ChatMsgNoTtsData -> !isWakeupState.get() || wakeupId.get()
                .toString() != chatMsgData.context?.context_id

            else -> true
        }
    }

    private fun handleSubtitleItems(chatMsgData: ChatMsgData) {
        lifecycleOwner.launch {
            chatMsgData.context?.subtitles?.items?.forEachIndexed { index, item ->
                val isFinal =
                    index == chatMsgData.context.subtitles.items.lastIndex && chatMsgData.is_final == 1
                logTagD(TAG, "========handleSubtitleItems============isFinal:$isFinal")
                ttsMessageQueue.get().trySend(TtsMessageData(item, false, isFinal))
            }
        }
    }

    private fun handleTextMessage(chatMsgData: ChatMsgNoTtsData) {

        chatMsgNoTtsQueue.add(chatMsgData)
        if (!isProcessingMessages.getAndSet(true)) {
            lifecycleOwner.launch {
                processChatMessages()
            }
        }

    }

    private suspend fun processChatMessages() = withContext(Dispatchers.IO) {
        while (isProcessingMessages.get()) {
            if (chatMsgNoTtsQueue.isEmpty()) break
            val chatMsgData = chatMsgNoTtsQueue.removeAt(0)
            val text = chatMsgData.context?.text ?: ""
            for ((index, char) in text.withIndex()) {
                val isFinal = chatMsgData.is_final == 1
                logTagD(TAG, "========handleTextMessage============isFinal:$isFinal")
                ttsMessageQueue.get().trySend(
                    TtsMessageData(
                        SubtitleItem(text = char.toString(), endTime = 150),
                        false,
                        isFinal
                    )
                )
            }
        }
        // 处理完所有消息后，重置标记
        isProcessingMessages.set(false)
    }

    private fun sendDefaultTtsMessage(chatMsgData: Any) {
        val isFinal = when (chatMsgData) {
            is ChatMsgData -> chatMsgData.is_final == 1
            is ChatMsgNoTtsData -> chatMsgData.is_final == 1
            else -> false
        }
        val item = SubtitleItem()
        logTagD(TAG, "========sendDefaultTtsMessage============isFinal:$isFinal")
        ttsMessageQueue.get().trySend(TtsMessageData(item, false, isFinal))
    }

}