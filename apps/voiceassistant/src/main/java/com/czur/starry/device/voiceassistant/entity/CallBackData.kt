package com.czur.starry.device.voiceassistant.entity

import com.google.gson.annotations.SerializedName

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/02/17
 */


data class AudioMetaData(
    @SerializedName("asr_fin") val asr_fin: <PERSON><PERSON><PERSON>,
    @SerializedName("text") val text: String,
    @SerializedName("result_type") val result_type: String,
    @SerializedName("context_id") val context_id: String,
    @SerializedName("version") val version: String
)

data class ChatMsgData(
    @SerializedName("result_type") val result_type: String,
    @SerializedName("context") val context: ContextData,
    @SerializedName("is_final") val is_final: Int,
    @SerializedName("version") val version: String
)
data class ChatMsgNoTtsData(
    @SerializedName("result_type") val result_type: String,
    @SerializedName("context") val context: ContextNoTimeData,
    @SerializedName("is_final") val is_final: Int,
    @SerializedName("version") val version: String
)


data class TtsMsgData(
    @SerializedName("result_type") val result_type: String,
    @SerializedName("context") val context: ContextTtsData,
    @SerializedName("version") val version: String
)

data class ContextTtsData(
    @SerializedName("context_id")
    val context_id: String,
)
data class ContextNoTimeData(
    @SerializedName("context_id")
    val context_id: String,
    @SerializedName("text")
    val text: String
)

data class ContextData(
    @SerializedName("context_id")
    val contextId: String,

    @SerializedName("subtitles")
    val subtitles: SubtitlesData
)

data class SubtitlesData(
    @SerializedName("subtitles")
    val items: List<SubtitleItem>
)

data class SubtitleItem(
    @SerializedName("Text")
    val text: String = "",

    @SerializedName("BeginTime")
    val beginTime: Int = 0,

    @SerializedName("EndTime")
    val endTime: Int = 0,

    @SerializedName("BeginIndex")
    val beginIndex: Int = 0,

    @SerializedName("EndIndex")
    val endIndex: Int = 0,

    @SerializedName("Phoneme")
    val phoneme: String = ""
)

data class InstructionResult(
    @SerializedName("intent") val intent: String,
    @SerializedName("target") val target: String,
    @SerializedName("param") val param: String
)

data class InstructionResponse(
    @SerializedName("instruction_result") val instructionResults: List<InstructionResult>,
    @SerializedName("result_type") val resultType: String,
    @SerializedName("version") val version: String
)


data class WakeUpResult(
    val result_type: String,
    val timestamp: Long,
    val version: String
)

data class MessageData(
    val text: String,
    val isHuman: Boolean,
    val isFinal: Boolean
)

data class TtsMessageData(
    val item: SubtitleItem,
    val isHuman: Boolean,
    val isFinal: Boolean
)


// 振幅数据对象
data class AmpData(
    val timestamp: Long, // 时间戳（毫秒）
    val amplitude: Double
)

// 每秒统计结果
data class SecondAverage(
    val second: Long,    // 精确到秒的时间戳（如 1711976238）
    val rawAverage: Double, // 原始平均值
    val displayValue: Float // 处理后的显示值
){
    companion object {
        // 专用计算方法
        fun calculateDisplayValue(avg: Double): Float {
            return "%.2f".format(avg)  // 先格式化为2位小数
                .replace(",", ".")     // 处理本地化小数点差异
                .toDouble()            // 转回Double类型
                .times(10)          // 放大10倍
                .toFloat()             // 最终转Float
        }
    }
}

// 事件处理接口
interface CommandEventHandler {
    fun handleCommand(event: CommandEvent)
}

// 事件数据类
data class CommandEvent(
    val intent: String,
    val target: String? = null,
    val params: String? = null,
    val isTouchPad: Boolean = false,
    val amplitude: Float = 0f,
    val isFinal: Boolean = false
)
