package com.czur.starry.device.voiceassistant.util

import android.content.Context
import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.voiceassistant.BuildConfig
import com.czur.starry.device.voiceassistant.entity.LocalPreItem
import com.czur.starry.device.voiceassistant.entity.PreloadData
import com.czur.starry.device.voiceassistant.manager.PreDataManager.saveLocalPreloadItems
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okio.IOException
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 *  author : <PERSON><PERSON>ao
 *  time   :2025/03/11
 */


object PreloadUtil {
    private const val TAG = "PreloadUtil"
    //预加载语音URL 测试
    private val VOICE_URL: String = if (Constants.isProductEnv) {
        "https://xing-asst.czur.com/config/default_voice"
    } else {
        "https://xing-asst-test.czur.com/config/default_voice"
    }

    private val gson by lazy { Gson() }

    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()



    private fun getSecondsTimestamp(): Long = System.currentTimeMillis() / 1000

    private fun generateAuthorization(timestamp: Long): String {
        return Constants.SERIAL + UserHandler.secret + timestamp + "xing"
    }

    private fun buildRequest(timestamp: Long, authorizationMd5: String): Request {
        return Request.Builder()
            .url(VOICE_URL)
            .get()
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .addHeader("Sn", Constants.SERIAL)
            .addHeader("Ts", timestamp.toString())
            .addHeader("authorization", authorizationMd5)
            .build()
    }

    /**
     * 请求预加载数据
     */
    suspend fun requestPreloadData(): Result<PreloadData> = withContext(Dispatchers.IO) {
        // 仅获取一次时间戳，保证一致性
        val timestamp = getSecondsTimestamp()
        val authorization = generateAuthorization(timestamp)
        val authorizationMd5 = authorization.md5().uppercase()
        logTagD(TAG, "authorizationMd5=$authorizationMd5")
        val request = buildRequest(timestamp, authorizationMd5)

        return@withContext try {
            val response = client.newCall(request).execute()
            if (response.isSuccessful) {
                response.body?.string()?.let {
                    val preloadData = parsePreloadData(it)
                    Result.success(preloadData)
                } ?: Result.failure(IOException("Response body is null"))
            } else {
                Result.failure(IOException("Request failed: ${response.code}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun parsePreloadData(json: String): PreloadData {
        try {
            val preloadData = gson.fromJson(json, PreloadData::class.java)
            return preloadData
        } catch (e: Exception) {
            e.printStackTrace()
            logTagE(TAG, "Failed to parse PreloadData from JSON ${e.message}")
            return PreloadData("0", emptyList())
        }
    }

    /**
     * 下载数据转换
     */
    suspend fun processPreloadData(context: Context, preloadData: PreloadData?) {
        preloadData?.preload?.let { preloadList ->
            val localPreItems = mutableListOf<LocalPreItem>()
            withContext(Dispatchers.IO) {
                for (preloadItem in preloadList) {
                    val fileName = "${preloadItem.key}.pcm"
                    val destination = File(context.filesDir, fileName)
                    val downloadResult = downloadFile(preloadItem.url, destination)
                    if (downloadResult.isSuccess) {
                        // 从 Result 中获取 File 对象
                        val file = downloadResult.getOrThrow()
                        val localPreItem = LocalPreItem(
                            key = preloadItem.key,
                            text = preloadItem.text,
                            absolutePath = file.absolutePath
                        )
                        localPreItems.add(localPreItem)
                    } else {
                        // 处理下载失败的情况，可根据需求添加日志记录或其他操作
                        logTagE(
                            TAG,
                            "Failed to download file for ${preloadItem.key}: ${downloadResult.exceptionOrNull()?.message}"
                        )
                    }
                }
            }
            saveLocalPreloadItems(localPreItems)
        }
    }


    /**
     * 下载语音文件
     */
    private fun downloadFile(url: String, destination: File): Result<File> {
        val request = Request.Builder()
            .url(url)
            .build()
        return try {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    throw IOException("Unexpected code $response")
                }
                response.body?.let { body ->
                    FileOutputStream(destination).use { outputStream ->
                        body.byteStream().copyTo(outputStream)
                    }
                } ?: throw IOException("Response body is null")
                Result.success(destination)
            }
        } catch (e: IOException) {
            logTagE(TAG, "Download file failed: ${e.message}")
            Result.failure(e)
        } catch (e: Exception) {
            logTagE(TAG, "Unexpected error during download: ${e.message}")
            Result.failure(e)
        }
    }


}