package com.czur.starry.device.voiceassistant.util

import android.app.ActivityManager
import android.content.Context
import com.czer.starry.device.meetlib.MeetingHandler.localMeetingRecording
import com.czer.starry.device.meetlib.MeetingHandler.localRecordMode
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantSupport
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.voiceassistant.R
import com.czur.starry.device.voiceassistant.common.Constants.WAKE_UP_EVENT
import com.czur.starry.device.voiceassistant.entity.CommandEvent
import com.czur.starry.device.voiceassistant.entity.CommandEventHandler
import com.czur.starry.device.voiceassistant.helper.LaunchForNavigate
import com.czur.starry.device.voiceassistant.util.FileSetUtil.RecordModel
import com.czur.starry.device.voiceassistant.util.FileSetUtil.setTranscription
import com.czur.starry.device.voiceassistant.util.FileSetUtil.stopAudioRecording
import com.czur.starry.device.voiceassistant.util.FileSetUtil.stopScreenRecording
import com.czur.starry.device.voiceassistant.util.FileSetUtil.stopVideoRecording
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 *  author : WangHao
 *  time   :2025/03/27
 *  用于处理快捷键的工具类
 */

private const val TAG = "ShortcutUtil"
class ShortcutUtil(
    private val context: Context,
    private val eventHandler: CommandEventHandler,
    private val launchForNavigate: LaunchForNavigate
) {
    private val activityManager by lazy { CZURAtyManager.appContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager }

    // 使用原子变量确保线程安全
    private var shortcutJob: Job? = null

    private  val HDMI_IN_CLASS = "com.ecloud.eairplay.MirrorActivity"

    enum class CustomShortcutEventType {
        SOUND_RECORD,           // 录音
        SCREEN_RECORD,          // 录屏
        VIDEO_RECORD,           // 录像
        TAKE_PICTURES,          // 拍照
        AI_SUBTITLES,           // AI字幕
    }

    fun handleShortcut(action: String?) {
        if (shortcutJob?.isActive == true) {
            //  避免双击快捷键的情况
            return
        }
        shortcutJob = CoroutineScope(Dispatchers.Main).launch {

            processRealAction(action)

            // 延迟1秒后重置状态
            delay(1000)
            shortcutJob = null
        }
    }

    private fun processRealAction(action: String?) {
        when (action) {
            WAKE_UP_EVENT -> {
                //判断当前是否 HDMI IN；USB外设；本地会议录制；小星同学关闭
                val isVoiceAssistantEnabled = getBooleanSystemProp("persist.sys.voice_assistant.enable", true)
                logTagD(TAG, "============isVoiceAssistantEnabled:$isVoiceAssistantEnabled")
                if (!isVoiceAssistantEnabled) {
                    if (!isVoiceAssistantSupport) {
                        NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                            put(context.getString(R.string.intent_wakeup_tip))
                        }
                    }else {
                        NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                            put(context.getString(R.string.intent_wakeup_not_support))
                        }
                    }
                }else {
                    eventHandler.handleCommand(CommandEvent(WAKE_UP_EVENT, isTouchPad = true))
                }
            }

            CustomShortcutEventType.SOUND_RECORD.name -> {
                if (localMeetingRecording) {
                    if (localRecordMode == RecordModel.AUDIO_MODE.name) {
                        stopAudioRecording()
                    } else {
                        NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                            put(context.getString(R.string.voice_command_recording_toast))
                        }
                    }

                } else {
                    if (isCurrentActivity()) {
                        return
                    }
                    MainScope().launch {
                        launchForNavigate.launchAndNavigate(
                            context.getString(R.string.app_name_local_record),
                            context.getString(R.string.voice_command_record)
                        )
                    }
                }
            }

            CustomShortcutEventType.SCREEN_RECORD.name -> {
                if (localMeetingRecording) {
                    if (localRecordMode == RecordModel.SCREEN_MODE.name) {
                        stopScreenRecording()
                    } else {
                        NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                            put(context.getString(R.string.voice_command_recording_toast))
                        }
                    }

                } else {
                    if (isCurrentActivity()) {
                        return
                    }
                    MainScope().launch {
                        launchForNavigate.launchAndNavigate(
                            context.getString(R.string.app_name_local_record),
                            context.getString(R.string.voice_command_screen)
                        )
                    }
                }

            }

            CustomShortcutEventType.VIDEO_RECORD.name -> {
                if (localMeetingRecording) {
                    if (localRecordMode == RecordModel.VIDEO_MODE.name) {
                        stopVideoRecording()
                    } else {
                        NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                            put(context.getString(R.string.voice_command_recording_toast))
                        }
                    }
                } else {
                    if (isCurrentActivity()) {
                        return
                    }
                    MainScope().launch {
                        launchForNavigate.launchAndNavigate(
                            context.getString(R.string.app_name_local_record),
                            context.getString(R.string.voice_command_video)
                        )
                    }
                }

            }

            CustomShortcutEventType.TAKE_PICTURES.name -> {
                if (localMeetingRecording && localRecordMode != RecordModel.CAMERA_MODE.name) {
                    NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                        put(context.getString(R.string.voice_command_recording_toast))
                    }
                } else {
                    if (isCurrentActivity()) {
                        return
                    }
                    MainScope().launch {
                        launchForNavigate.launchAndNavigate(
                            context.getString(R.string.app_name_local_record),
                            context.getString(R.string.voice_command_photo)
                        )
                    }
                }

            }
            CustomShortcutEventType.AI_SUBTITLES.name -> {
                if (TransHandler.isTranslating) {
                    TransHandler.stopTrans = true
                    TransHandler.isTranslating = false
                }else {
                    setTranscription(true)
                }

            }

            else -> {}
        }
    }


    /**
     * 判断当前Activity
     */
    private fun isCurrentActivity(): Boolean {
        val tasks = activityManager.getRunningTasks(Int.MAX_VALUE) ?: return false
        if (tasks.isNotEmpty()) {
            for (task in tasks) {
                if (task.topActivity?.className == HDMI_IN_CLASS) {
                    logTagD(TAG, "当前Activity: $HDMI_IN_CLASS")
                    NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                        put(context.getString(R.string.voice_command_hdmi_in_toast))
                    }
                    return true
                }
            }
        }
        return false
    }
}