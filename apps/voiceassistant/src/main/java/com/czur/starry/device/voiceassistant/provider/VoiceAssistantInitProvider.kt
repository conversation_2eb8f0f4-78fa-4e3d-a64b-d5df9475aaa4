package com.czur.starry.device.voiceassistant.provider

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.handler.SPContentHandler
import com.czur.starry.device.baselib.handler.SPContentProvider
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/06/09
 */


class VoiceAssistantInitProvider: SPContentProvider() {


    override val Context.providerDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "VoiceAssistantInitProvider",
        corruptionHandler = createDefCorruptionHandler("VoiceAssistantInitProvider")
    )
    override val targetHandler: SPContentHandler by lazy { TransHandler }


    override fun onSubQuery(key: String, defValue: String): String? {
        return super.onSubQuery(key, defValue)
    }

    override fun onSubUpdate(key: String, value: String): Boolean {
        return super.onSubUpdate(key, value)
    }

    override fun doOnDataStoreError(tr: Throwable) {
        logTagE("OTAProvider", "DataStore异常", tr = tr)
        val file = context!!.filesDir
        file.deleteRecursively()
    }
}