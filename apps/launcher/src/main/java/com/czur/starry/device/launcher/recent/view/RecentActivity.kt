package com.czur.starry.device.launcher.recent.view

import android.app.WallpaperManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.PointerIcon
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.databinding.ActivityRecentAppBinding
import com.czur.starry.device.launcher.recent.adapter.RecentRecycleAdapter
import com.czur.starry.device.launcher.recent.vm.RecentViewModel
import com.czur.starry.device.launcher.recent.widget.PagerGridLayoutManager
import com.czur.starry.device.launcher.recent.widget.PagerGridLayoutManager.PagerChangedListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File


/**
 *  author : WangHao
 *  time   :2024/05/24
 */

private const val ACTION_FINISH_RECENT = "android.intent.action.recent.finish"
private const val ACTION_RECENT_START = "czur.intent.action.APP_RESUME"
private const val CATEGORY_RECENT = "czur.intent.category.RECENT"

class RecentActivity : CZViewBindingAty<ActivityRecentAppBinding>() {
    companion object {
        private const val TAG = "RecentActivity"
        const val RECENT_TASK_THUMBNAIL_PATH = "sdcard/recent/lastTaskScreenshot.png"
        const val RECENT_FILE_PATH = "sdcard/recent"
    }

    private val recentViewModel: RecentViewModel by viewModels()
    private val recentRecycleAdapter = RecentRecycleAdapter()

    private val finishReceiver = FinishRecentReceiver()

    private var systemAnimScale = 1

    private var indexRb: RadioGroup? = null
    private lateinit var layoutManager: PagerGridLayoutManager
    override fun AtyParams.initAtyParams() {
        lifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onStart(owner: LifecycleOwner) {
                super.onStart(owner)
                systemAnimScale = Settings.Global.getInt(contentResolver, "transition_animation_scale", 1)
                Settings.Global.putInt(contentResolver, "transition_animation_scale", 0)
                val intentFilter = IntentFilter(ACTION_FINISH_RECENT)
                ContextCompat.registerReceiver(this@RecentActivity,finishReceiver,intentFilter,ContextCompat.RECEIVER_EXPORTED)
            }

            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                unregisterReceiver(finishReceiver)
                launch {
                    clearLastRecentThumbnail()
                }
                logTagD(TAG, "恢复过渡动画缩放:${systemAnimScale}")
                Settings.Global.putInt(contentResolver, "transition_animation_scale", systemAnimScale)

            }
        }
    }

    private val wallpaperManager: WallpaperManager by lazy(LazyThreadSafetyMode.NONE) {
        WallpaperManager.getInstance(globalAppCtx)
    }

    override fun initWindow() {
        super.initWindow()
        wallpaperManager.drawable?.let {
            window.setBackgroundDrawable(it)
        }

    }

    override fun ActivityRecentAppBinding.initBindingViews() {
        startHomeClickView.setOnDebounceClickListener {
            startHome()
        }
        appRecentRv.setOnDebounceClickListener {
            startHome()
        }

        // 清理最近任务
        clearTaskIv.setOnDebounceClickListener {
            launch {
                recentViewModel.clearRecentTask()
                finish()
            }
        }

        // 初始化RecyclerView
        layoutManager =
            PagerGridLayoutManager(
                RecentViewModel.RECENT_PAGE_ROW,
                RecentViewModel.RECENT_PAGE_COLUMN,
                PagerGridLayoutManager.HORIZONTAL
            ).apply {
                this.setPagerChangedListener(object : PagerChangedListener {
                    override fun onPagerCountChanged(pagerCount: Int) {
                    }

                    override fun onPagerIndexSelected(prePagerIndex: Int, currentPagerIndex: Int) {
                        recentViewModel.updatePageIndex(currentPagerIndex)
                    }

                })

            }
        appRecentRv.layoutManager = layoutManager
        appRecentRv.closeDefChangeAnimations()
        appRecentRv.adapter = recentRecycleAdapter

        // 点击事件
        appRecentRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            when (view.id) {
                R.id.appDeleteIv -> {
                    val task = recentRecycleAdapter.getData(pos)
                    val needFinish = recentRecycleAdapter.itemCount <= 1
                    launch {
                        recentViewModel.clearRecentTask(task)
                        if (needFinish) {
                            logTagD(TAG, "只有一个任务,需要返回Launcher")
                            startHome()
                        }
                    }
                }

                R.id.appRecentClickView -> {
                    // 启动任务
                    val taskID = recentRecycleAdapter.getData(pos).taskId
                    launch {
                        if (pos == 0 && recentViewModel.getRunningTaskId() == taskID) {
                            finish()
                        } else {
                            recentViewModel.startTask(taskID)
                        }

                    }
                }

                else -> {
                    startHome()
                }
            }
            true
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(recentViewModel.recentAppsFlow) {
            logTagD(TAG, "recentAppsFlow.observe=$it")
            recentRecycleAdapter.setData(it)
            binding.emptyGroup.gone(it.isNotEmpty())
            binding.actionGroup.gone(it.isEmpty())
            binding.pageIndexPointGroupContainer.invisible(it.isEmpty())
        }

        // 页数
        repeatCollectOnResume(recentViewModel.pageInfoFlow) { (pageIndex, pageCount) ->
            updatePageIndexGroup(pageIndex, pageCount)
        }

        repeatOnResume {
            launch {
                recentViewModel.loadTasks()
            }
        }

        repeatOnResume {
            logTagV(TAG, "sendBroadcast ACTION_RECENT_START")
            sendBroadcast(Intent(ACTION_RECENT_START).apply {
                addCategory(CATEGORY_RECENT)
            })
        }

        // 模糊壁纸
        repeatCollectOnResume(recentViewModel.clearBarBmpFlow) {
            if (it == null) {
                binding.clearBarIv.alpha = 0F
            } else {
                binding.clearBarIv.alpha = 1F
                binding.clearBarIv.setImageBitmap(it)
            }
        }
    }

    /**
     * 更新页数指示器
     */
    private fun updatePageIndexGroup(pageIndex: Int, pageCount: Int) {
        logTagD(TAG, "pageIndex=$pageIndex, pageCount=$pageCount")
        if (pageCount == pageIndex) {
            logTagW(TAG, "pageIndex=$pageIndex, pageCount=$pageCount")
            return
        }
        if (pageCount == 0) {
            binding.pageIndexPointGroupContainer.removeAllViews()
            return
        }

        if (indexRb == null) {
            indexRb = LayoutInflater.from(this)
                .inflate(
                    R.layout.view_recent_index_point_group,
                    binding.pageIndexPointGroupContainer,
                    false
                ) as RadioGroup
            binding.pageIndexPointGroupContainer.addView(indexRb)
        }

        val currentCount = indexRb?.childCount ?: 0
        if (currentCount < pageCount) {
            val needAddCount = pageCount - currentCount
            logTagV(TAG, "添加索引点:${needAddCount}个")
            repeat(needAddCount) {
                logTagV(TAG, "添加第:${it}个指示器")
                val pointView = LayoutInflater.from(this)
                    .inflate(R.layout.view_recent_index_point, indexRb, false)
                indexRb!!.addView(pointView)
            }

            // 添加点击事件
            for (i in 0 until pageCount) {
                val pointView = indexRb!!.getChildAt(i) as? RadioButton
                pointView?.pointerIcon =
                    PointerIcon.getSystemIcon(this, PointerIcon.TYPE_ARROW) // 防止出现小手
                pointView?.setOnDebounceClickListener {
                    layoutManager.smoothScrollToPagerIndex(i)
                }
            }


        } else if (currentCount > pageCount) {
            // 索引点减少时,全部移出重新加载, 因为宽高没有办法重新计算
            binding.pageIndexPointGroupContainer.removeAllViews()
            indexRb = null
            updatePageIndexGroup(pageIndex, pageCount)
            return
        }

        // 更新指示器
        val currentPointView = indexRb?.getChildAt(pageIndex) as? RadioButton
        currentPointView?.isChecked = true
    }


    override fun onStop() {
        super.onStop()
        if (!isFinishing) {
            finish()
        }
    }

    /**
     * 回到桌面
     */
    private fun startHome() {
        logTagI(TAG, "startHome")
        val homeIntent = Intent(Intent.ACTION_MAIN).apply {
            addCategory(Intent.CATEGORY_HOME)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        startActivity(homeIntent)
        finish()
    }

    /**
     * 清除最近task截图
     */
    private suspend fun clearLastRecentThumbnail() = withContext(Dispatchers.IO) {
        File(RECENT_TASK_THUMBNAIL_PATH).delete()
    }

    /**
     * 系统发出, 三指下滑时,关闭最近任务页面
     */
    private inner class FinishRecentReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            logTagI(TAG, "FinishRecentReceiver")
            finish()
        }
    }
}