package com.czur.starry.device.launcher.widget

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.withStyledAttributes
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.Dark
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.Light
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2025/6/10
 */
class ThemeImageView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private val scope = MainScope()
    private var job: Job? = null

    private var lightSrc: Int = 0
    private var darkSrc: Int = 0

    init {
        // 读取TA
        context.withStyledAttributes(attrs, R.styleable.ThemeImageView) {
            lightSrc = getResourceId(R.styleable.ThemeImageView_lightScr, 0)
            darkSrc = getResourceId(R.styleable.ThemeImageView_darkScr, 0)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        job?.cancel()
        job = scope.launch {
            BackdropManager.backdropColorFlow.collect {
                updateSrc(it)
            }
        }
    }

    private fun updateSrc(backdropColor: BackdropColor) {
        val targetSrc = when (backdropColor) {
            Dark -> darkSrc
            Light -> lightSrc
        }
        if (targetSrc != 0) {
            setImageResource(targetSrc)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        job?.cancel()
    }

}