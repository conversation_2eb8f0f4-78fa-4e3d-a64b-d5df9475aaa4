package com.czur.starry.device.launcher.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.withStyledAttributes
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.Dark
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.Light
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2025/6/10
 */
class ThemeTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private val scope = MainScope()
    private var job: Job? = null


    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        job?.cancel()
        job = scope.launch {
            BackdropManager.backdropColorFlow.collect {
                updateShadow(it)
            }
        }
    }

    private fun updateShadow(backdropColor: BackdropColor) {
        when (backdropColor) {
            Dark -> {
                // 删除阴影
                setShadowLayer(0F, 0F, 0F, Color.TRANSPARENT)
            }

            Light -> {
                setShadowLayer(1F, 0F, 1F, 0x80000000.toInt())
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        job?.cancel()
    }

}