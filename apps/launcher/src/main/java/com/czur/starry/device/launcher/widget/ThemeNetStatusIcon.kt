package com.czur.starry.device.launcher.widget

import android.content.Context
import android.util.AttributeSet
import android.view.ViewTreeObserver
import androidx.appcompat.widget.AppCompatImageView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.requireLifecycleOwner
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

private const val TAG = "ThemeNetStatusIcon"

class ThemeNetStatusIcon @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr), ViewTreeObserver.OnGlobalLayoutListener {

    private val netStatusUtil: NetStatusUtil

    // 当前图标的网络状态
    private var netStatus = NetStatusUtil.NetStatus.NO_NET_WORK

    private var job: Job? = null
    private val scope = MainScope()
    private var currentIcon: IconData = IconData.WifiNoNetworkIcon

    /**
     * 网络状态图标的点击事件
     * status: 当前的网络状态
     */
    var onNetViewClick: ((status: NetStatusUtil.NetStatus) -> Unit)? = null
        set(value) {
            if (value != null) {
                field = value
                setOnClickListener {
                    onNetViewClick?.invoke(netStatus)
                }
            } else {
                setOnClickListener(null)
            }
        }

    init {
        viewTreeObserver.addOnGlobalLayoutListener(this)
        netStatusUtil = NetStatusUtil(context)
    }

    private fun setWifiImg(signalLevel: Int) {
        when (signalLevel) {
            1 -> updateImage(IconData.WifiWeakIcon)
            2 -> updateImage(IconData.WifiMidIcon)
            3 -> updateImage(IconData.WifiStrongIcon)
            else -> {
                logTagW(
                    TAG,
                    "信号强度范围应当是0-2,当前信号强度为:${signalLevel}"
                )
                updateImage(IconData.WifiBadIcon)
            }
        }
    }

    private fun updateImage(iconData: IconData = currentIcon) {
        when (BackdropManager.backdropColor) {
            BackdropColor.Light -> setImageResource(iconData.lightIcon)
            BackdropColor.Dark -> setImageResource(iconData.darkIcon)
        }
        currentIcon = iconData
    }


    /**
     * 当UI加载完成后,再进行网络状态监控
     */
    override fun onGlobalLayout() {
        viewTreeObserver.removeOnGlobalLayoutListener(this)
        val lifecycleOwner = requireLifecycleOwner()
        // 监听网络状态的改变
        netStatusUtil.netStatus.observe(lifecycleOwner) {
            logTagD(TAG, "状态:${it}")
            netStatus = it
            when (it) {
                NetStatusUtil.NetStatus.NO_NET_WORK -> updateImage(IconData.WifiNoNetworkIcon)
                NetStatusUtil.NetStatus.WIFI -> setWifiImg(netStatusUtil.getWifiSignalLevel())
                NetStatusUtil.NetStatus.ETHERNET -> {
                    if (netStatusUtil.internetStatusLive.value == InternetStatus.CONNECT) {
                        updateImage(IconData.Ethernet)
                    } else {
                        updateImage(IconData.EthUnidentifiedIcon)
                    }
                }

                else -> {
                }
            }

        }


        //仅有线网使用时，判断是否ping通外网
        netStatusUtil.internetStatusLive.observe(lifecycleOwner) {
            if (netStatusUtil.netStatus.value == NetStatusUtil.NetStatus.ETHERNET) {
                if (InternetStatus.CONNECT == it) {
                    updateImage(IconData.Ethernet)
                } else {
                    updateImage(IconData.EthUnidentifiedIcon)
                }
            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        job?.cancel()
        // 当处于可见生命周期,才开始监控网络状态
        netStatusUtil.startWatching()
        job = scope.launch {
            BackdropManager.backdropColorFlow.collect {
                updateImage()
            }
        }
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        job?.cancel()
        // 当处于不可见生命周期时, 停止监控网络状态
        netStatusUtil.stopWatching()
    }

    private sealed class IconData(
        val lightIcon: Int,
        val darkIcon: Int
    ) {

        object Ethernet : IconData(
            lightIcon = R.drawable.icon_status_ethernet_shadow,
            darkIcon = R.drawable.icon_status_ethernet_normal
        )

        object EthUnidentifiedIcon : IconData(
            lightIcon = R.drawable.icon_status_ethernet_unidentified_shadow,
            darkIcon = R.drawable.icon_status_ethernet_unidentified_normal
        )

        object WifiNoNetworkIcon : IconData(
            lightIcon = R.drawable.icon_status_no_network_shadow,
            darkIcon = R.drawable.icon_status_no_network_normal
        )

        object WifiBadIcon : IconData(
            lightIcon = R.drawable.icon_status_wifi_bad_shadow,
            darkIcon = R.drawable.icon_status_wifi_bad_normal
        )

        object WifiStrongIcon : IconData(
            lightIcon = R.drawable.icon_status_wifi_strong_shadow,
            darkIcon = R.drawable.icon_status_wifi_strong_normal
        )

        object WifiMidIcon : IconData(
            lightIcon = R.drawable.icon_status_wifi_mid_shadow,
            darkIcon = R.drawable.icon_status_wifi_mid_normal
        )

        object WifiWeakIcon : IconData(
            lightIcon = R.drawable.icon_status_wifi_weak_shadow,
            darkIcon = R.drawable.icon_status_wifi_weak_normal
        )
    }


}