package com.czur.starry.device.localmeetingrecord.services

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.annotation.LayoutRes
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants.ANIM_DURATION_SHORT
import com.czur.starry.device.baselib.utils.createNotificationChannel
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_HEIGHT
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_WIDTH
import com.czur.starry.device.localmeetingrecord.R
import com.czur.starry.device.localmeetingrecord.services.MeetingRecordAlertWindowService.Edge.BOTTOM
import com.czur.starry.device.localmeetingrecord.services.MeetingRecordAlertWindowService.Edge.LEFT
import com.czur.starry.device.localmeetingrecord.services.MeetingRecordAlertWindowService.Edge.RIGHT
import com.czur.starry.device.localmeetingrecord.services.MeetingRecordAlertWindowService.Edge.TOP
import com.czur.starry.device.sharescreen.esharelib.SimpleEShareCallback
import com.eshare.serverlibrary.api.EShareServerSDK
import com.noober.background.BackgroundLibrary
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty


/**
 * Created by 陈丰尧 on 2023/2/13
 */
abstract class MeetingRecordAlertWindowService : LifecycleService() {
    companion object {
        private const val PRIVATE_FLAG_NO_MOVE_ANIMATION = 1 shl 6
    }

    private val TAG = "MeetingRecordAlertWindowService"
    private val COME_IN = 0
    private val COME_OUT = 1

    private val SMALL_SHORT_63 = 63
    private val SMALL_LONG_105 = 105
    private val BIG_SHORT_105 = AUDIO_FLOAT_HEIGHT
    private val BIG_LONG_416 = AUDIO_FLOAT_WIDTH
    private val NARROW_DOWN_TIME = 3000 //

    open val channel_id = "LocalMeetingRecordChannel"
    open val channel_id_num = 1

    @get:LayoutRes
    abstract var layoutId: Int

    // window 的宽高
    abstract var windowWidth: Int
    abstract var windowHeight: Int

    var isPause = false // 暂停情况,不触发缩小动画

    var saveLayoutId = 0
    var windowWidthRevise = -1 // 允许重新设置宽度
    var windowHeightRevise = -1 // 允许重新设置高度

    val currentWindowWidth: Int
        get() {
            return if (windowWidthRevise == -1) {
                windowWidth
            } else {
                windowWidthRevise
            }
        }

    val currentWindowHeight: Int
        get() {
            return if (windowHeightRevise == -1) {
                windowHeight
            } else {
                windowHeightRevise
            }
        }
    open var xOffSet: Int = 0
    open var yOffset: Int = 0

    open val windowType: Int? = null
    open val careKeyEvent: Boolean = false  // 是否关心按键

    open val draggable = false //是否可以拖拽

    open val keepScreenOn = true // 是否需要屏幕保持常亮

    open var needNarrowToCircle = false // 是否需要缩小到边框的功能
        set(value) {
            field = value
            FloatingListener().autoAdsorptionToEdge()
            startNarrowLauncher()
        }

    private var isAutoAnimPlaying = false   // 自动吸附动画是否正在播放

    protected var rootView: View? = null
        private set
    protected val isViewAttached: Boolean
        get() = rootView != null
    private var floatingListener: FloatingListener? = null

    var wmParams: WindowManager.LayoutParams? = null

    protected val windowManager by lazy {
        getSystemService(WINDOW_SERVICE) as WindowManager
    }

    /**
     * 是否要阻止显示
     */
    private var blockDisplay: Boolean = false

    private var isSmallCircleView = false// 当前是否是缩小的圆型图标View
    private var nowEdge = LEFT


    var narrowTimeDownTime = System.currentTimeMillis()
    private var narrowToCircleJob: Job? = null


    val eShareSDK = EShareServerSDK.getSingleton(CZURAtyManager.appContext)

    var byomIsPairing = false
    var notifyPairStateJob: Job? = null
    private val eShareCallback = object : SimpleEShareCallback() {
        override fun notifyPairState(pairState: Int) {
            super.notifyPairState(pairState)
            // 如果在2和3之间,调用了request 不处理
            if (pairState == 2) {
                byomIsPairing = true
                notifyPairStateJob = launch {
                    delay(5000)
                    byomIsPairing = false
                }

            } else {
                notifyPairStateJob?.cancel()
                byomIsPairing = false
            }
        }

        override fun showRequestAlert(clientIp: String?, clientName: String?, clientType: Int) {
        }
    }


    override fun onCreate() {
        super.onCreate()
        saveLayoutId = layoutId
        eShareSDK.registerCallback(eShareCallback)
        launch {
            initTask()
            blockDisplay = needBlockDisplay()
            if (!blockDisplay) {
                createWindowView()
                initData()
            } else {
                stopSelf()
            }
        }
    }

    private fun startNarrowLauncher() {
        narrowToCircleJob?.cancel()
        narrowToCircleJob = launch(Dispatchers.IO) {
            narrowTimeDownTime = System.currentTimeMillis()

            if (!needNarrowToCircle || isSmallCircleView) {
                cancel()
            }
            while (true) {

                if (System.currentTimeMillis() - narrowTimeDownTime > NARROW_DOWN_TIME && !isPause) {
                    withContext(Dispatchers.Main) {
                        bigMainViewAnim(nowEdge, COME_OUT)
                    }
                    cancel()
                    break
                }
                delay(300)
            }
        }
    }

    open suspend fun initTask() {}

    open fun needBlockDisplay(): Boolean {
        return false
    }

    fun refreshParams(width: Int, height: Int) {
        windowHeight = height
        windowWidth = width
        // 获取根视图的布局参数
        wmParams?.apply {
            this.width = windowWidth
            this.height = windowHeight

            windowWidthRevise = width
            windowHeightRevise = height
            try {
                val privateFlagsField = this.javaClass.getField("privateFlags")
                val currentFlags = privateFlagsField.get(this) as Int
                privateFlagsField.set(this, currentFlags or PRIVATE_FLAG_NO_MOVE_ANIMATION)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            updateViewLayout(this)
        }
        FloatingListener().autoAdsorptionToEdge()
    }

    fun refreshParams(width: Int, height: Int, xOffSet: Int, yOffset: Int) {
        windowHeight = height
        windowWidth = width
        wmParams?.apply {
            this.width = width
            this.height = height
            x = xOffSet
            y = yOffset

            updateViewLayout(this)
        }
        windowWidthRevise = width
        windowHeightRevise = height
    }

    private fun createWindowView() {
        logTagI(TAG, "createWindowView")
        wmParams = WindowManager.LayoutParams().apply {
            // 设置大小
            width = windowWidth
            height = windowHeight
            type = if (windowType != null) {
                windowType!!
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
            }

            gravity = Gravity.START or Gravity.TOP
            if (!careKeyEvent) {
                flags =
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            }

            if (keepScreenOn) {
                // 保持屏幕常亮
                flags = flags or WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
            }

            format = PixelFormat.RGBA_8888
            x = xOffSet
            y = yOffset
            try {
                val privateFlagsField = wmParams!!.javaClass.getField("privateFlags")
                val currentFlags = privateFlagsField.get(wmParams) as Int
                privateFlagsField.set(wmParams, currentFlags or PRIVATE_FLAG_NO_MOVE_ANIMATION)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 这里如果实装悬浮显示的化, 似乎应该改成inject2方法
        val inflater = BackgroundLibrary.inject(this)
        rootView = inflater.inflate(layoutId, null).apply {
            // 将悬浮窗控件添加到WindowManager
            windowManager.addView(this, wmParams)
        }

        floatingListener = FloatingListener()
        rootView?.apply {
            if (draggable) {
                setOnTouchListener(floatingListener)
            }

            if (careKeyEvent) {
                rootView.isFocusableInTouchMode = true
                rootView.setOnKeyListener { v, keyCode, event ->
                    <EMAIL>(keyCode, event)
                }
            }

            if (!isSmallCircleView) {
                initViews()

                if (needNarrowToCircle) {
                    startNarrowLauncher()
                }

                rootView.setOnHoverListener { v, event ->
                    if (event.action == MotionEvent.ACTION_HOVER_ENTER || event.action == MotionEvent.ACTION_HOVER_MOVE) {
                        narrowTimeDownTime = System.currentTimeMillis()
                    }
                    false
                }
            }
        }
    }

    abstract fun View.initViews()

    open fun initData() {}

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (!blockDisplay) {
            onDataRefresh(intent)
        }

        createNotificationChannel(channel_id, channel_id)
        val notification: NotificationCompat.Builder = NotificationCompat.Builder(this, channel_id)
            .setContentTitle(channel_id)
            .setSmallIcon(R.mipmap.icon)
            .setOnlyAlertOnce(true)
            .setOngoing(true)
        startForeground(channel_id_num, notification.build())

        return super.onStartCommand(intent, flags, startId)
    }

    open fun onDataRefresh(intent: Intent?) {}

    open fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean = false

    protected fun releaseRootView() {
        rootView?.let {
            try {
                if (it.isAttachedToWindow) {
                    windowManager.removeViewImmediate(it)
                }
            } catch (e: IllegalArgumentException) {
                logTagE(TAG, "View already removed: ${e.message}")
            }
        }
    }

    override fun onDestroy() {
        releaseRootView()
        super.onDestroy()
        eShareSDK.unregisterCallback(eShareCallback)
    }

    private fun updateViewLayout(params: WindowManager.LayoutParams) {
        try {
            windowManager.updateViewLayout(rootView, params)
        } catch (exp: Exception) {
            logTagW(TAG, "updateViewLayout 失败", tr = exp)
        }
    }

    protected inner class ViewFinder<T : View>(
        private val viewID: Int
    ) : ReadOnlyProperty<Any, T> {
        private var cacheView: T? = null
        override fun getValue(thisRef: Any, property: KProperty<*>): T {
            return cacheView ?: (rootView!!.findViewById<T>(viewID)).also {
                cacheView = it
            }
        }
    }

    fun isTouchingWindow(): Boolean {
        return floatingListener?.isTouching ?: false
    }


    private inner class FloatingListener : View.OnTouchListener {
        //开始触控的坐标，移动时的坐标（相对于屏幕左上角的坐标）
        private var mTouchStartX: Int = 0
        private var mTouchStartY: Int = 0
        private var mTouchCurrentX: Int = 0
        private var mTouchCurrentY: Int = 0
        var isTouching = false

        //开始时的坐标和结束时的坐标（相对于自身控件的坐标）
        private var mStartX: Int = 0
        private var mStartY: Int = 0

        @SuppressLint("ClickableViewAccessibility")
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            if (isAutoAnimPlaying) {
                // 正在移动动画的时候, 禁止拖动
                return true
            }
            // 检查是否是鼠标滚轮事件
            if ((event.source and android.view.InputDevice.SOURCE_MOUSE) != 0 &&
                event.getAxisValue(MotionEvent.AXIS_VSCROLL) != 0f
            ) {
                return false
            }
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    narrowToCircleJob?.cancel()
                    mTouchStartX = event.rawX.toInt()
                    mTouchStartY = event.rawY.toInt()
                    mStartX = event.x.toInt()
                    mStartY = event.y.toInt()
                    isTouching = true
                }

                MotionEvent.ACTION_MOVE -> {
                    if (isSmallCircleView) {
                        return true
                    }
                    mTouchCurrentX = event.rawX.toInt()
                    mTouchCurrentY = event.rawY.toInt()
                    val xNew = mTouchCurrentX - mTouchStartX + wmParams?.x!!
                    val yNew = mTouchCurrentY - mTouchStartY + wmParams?.y!!
                    wmParams?.apply {

                        x = if (xNew < 10) {
                            10
                        } else
                            if (xNew + windowWidth > getScreenWidth() - 10) {
                                getScreenWidth() - windowWidth - 10
                            } else {
                                xNew
                            }
                        y = if (yNew < 10) {
                            10
                        } else
                            if (yNew + windowHeight > getScreenHeight() - 10) {
                                getScreenHeight() - windowHeight - 10
                            } else {

                                yNew
                            }

                        updateViewLayout(this)
                    }
                    mTouchStartX = mTouchCurrentX
                    mTouchStartY = mTouchCurrentY
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (isSmallCircleView) {
                        smallCircleViewAnim(nowEdge, COME_OUT)
                        return true
                    }
                    startNarrowLauncher()
                    autoAdsorptionToEdge()
                    isTouching = false
                }

                else -> {}
            }
            //如果是移动事件不触发OnClick事件，防止移动的时候一放手形成点击事件
            return true
        }

        /**
         * 把悬浮窗自动吸附到边缘
         */
        fun autoAdsorptionToEdge() {
            wmParams?.apply {
                val left = x
                val top = y
                val right = getScreenWidth() - x - currentWindowWidth
                val bottom = getScreenHeight() - y - currentWindowHeight

                val edge = when {
                    left <= top && left <= right && left <= bottom -> LEFT
                    top <= left && top <= right && top <= bottom -> TOP
                    right <= left && right <= top && right <= bottom -> RIGHT
                    else -> BOTTOM
                }

                nowEdge = edge

                val anim = when (edge) {
                    LEFT -> ValueAnimator.ofInt(x, 10)
                    TOP -> ValueAnimator.ofInt(y, 10)
                    RIGHT -> ValueAnimator.ofInt(x, getScreenWidth() - currentWindowWidth - 10)
                    BOTTOM -> ValueAnimator.ofInt(
                        y,
                        getScreenHeight() - currentWindowHeight - 10
                    )
                }
                anim.duration = ANIM_DURATION_SHORT
                anim.interpolator = AccelerateDecelerateInterpolator()
                // 为动画添加完成回调
                anim.doOnStart {
                    isAutoAnimPlaying = true
                }
                anim.doOnEnd {
                    isAutoAnimPlaying = false
                    narrowToCircleJob?.start()
                }
                anim.addUpdateListener {
                    when (edge) {
                        LEFT -> x = it.animatedValue as Int
                        TOP -> y = it.animatedValue as Int
                        RIGHT -> x = it.animatedValue as Int
                        BOTTOM -> y = it.animatedValue as Int
                    }
                    updateViewLayout(this)
                }
                anim.start()
            }


        }
    }

    // 主操作栏,自动吸附倒计时3秒后,缩到屏幕内,显示一个小半圆
    private fun bigMainViewAnim(edge: Edge, direction: Int) {
        wmParams?.apply {
            val anim = if (direction == COME_OUT) {
                when (edge) {
                    LEFT -> ObjectAnimator.ofFloat(
                        rootView,
                        "translationX",
                        0f,
                        -currentWindowWidth.toFloat()
                    )

                    TOP -> ObjectAnimator.ofFloat(
                        rootView,
                        "translationY",
                        0f,
                        -currentWindowHeight.toFloat()
                    )

                    RIGHT -> ObjectAnimator.ofFloat(
                        rootView,
                        "translationX",
                        0f,
                        currentWindowWidth.toFloat()
                    )

                    BOTTOM -> ObjectAnimator.ofFloat(
                        rootView,
                        "translationY",
                        0f,
                        currentWindowHeight.toFloat()
                    )
                }
            } else {
                when (edge) {
                    LEFT -> ObjectAnimator.ofFloat(
                        rootView,
                        "translationX",
                        -currentWindowWidth.toFloat(), 0f
                    )

                    TOP -> ObjectAnimator.ofFloat(
                        rootView,
                        "translationY",
                        -currentWindowHeight.toFloat(), 0f
                    )

                    RIGHT -> ObjectAnimator.ofFloat(
                        rootView,
                        "translationX",
                        currentWindowWidth.toFloat(), 0f
                    )

                    BOTTOM -> ObjectAnimator.ofFloat(
                        rootView,
                        "translationY",
                        currentWindowHeight.toFloat(), 0f
                    )
                }
            }

            anim.duration = ANIM_DURATION_SHORT
            anim.interpolator = AccelerateDecelerateInterpolator()
            // 为动画添加完成回调
            anim.doOnStart {
                isAutoAnimPlaying = true
            }
            anim.doOnEnd {
                launch {

                    if (direction == COME_IN) {
                        delay(100)
                        isAutoAnimPlaying = false
                        return@launch
                    }

                    rootView?.visibility = View.GONE
                    windowManager.removeView(rootView)
                    delay(100)


                    smallCircleViewAnim(edge, COME_IN)
//                    delay(1500)
//                    createSmallCircleViewAnim(edge, COME_OUT)
                }

            }
            anim.addUpdateListener {
            }
            anim.start()
        }

    }

    // 小圆盘出现动画
    private fun smallCircleViewAnim(nowEdge: Edge, direction: Int) {
        isSmallCircleView = true

        when (nowEdge) {// 设置小窗口的尺寸和位置相关信息
            LEFT -> {
                windowWidth = SMALL_SHORT_63
                windowHeight = SMALL_LONG_105
                layoutId = R.layout.float_small_circle_left_layout
                xOffSet = 0
                yOffset = wmParams!!.y
            }

            TOP -> {
                windowWidth = SMALL_LONG_105
                windowHeight = SMALL_SHORT_63
                layoutId = R.layout.float_small_circle_top_layout
                xOffSet = wmParams!!.x
                yOffset = 0
            }

            RIGHT -> {
                xOffSet = getScreenWidth() - SMALL_SHORT_63
                yOffset = wmParams!!.y
                windowWidth = SMALL_SHORT_63
                windowHeight = SMALL_LONG_105
                layoutId = R.layout.float_small_circle_right_layout
            }

            BOTTOM -> {
                xOffSet = wmParams!!.x
                yOffset = getScreenWidth() - SMALL_SHORT_63
                windowWidth = SMALL_LONG_105
                windowHeight = SMALL_SHORT_63
                layoutId = R.layout.float_small_circle_bottom_layout

            }
        }

        if (direction == COME_IN) {
            createWindowView()
        }
        val anim = if (direction == COME_IN) {
            when (nowEdge) {
                LEFT -> ObjectAnimator.ofFloat(
                    rootView,
                    "translationX",
                    -SMALL_SHORT_63.toFloat(),
                    0f
                )

                TOP -> ObjectAnimator.ofFloat(
                    rootView,
                    "translationY",
                    -SMALL_SHORT_63.toFloat(),
                    0f
                )

                RIGHT -> ObjectAnimator.ofFloat(
                    rootView,
                    "translationX",
                    SMALL_SHORT_63.toFloat(),
                    0f
                )

                BOTTOM -> ObjectAnimator.ofFloat(
                    rootView,
                    "translationY",
                    SMALL_SHORT_63.toFloat(),
                    0f
                )
            }
        } else {
            when (nowEdge) {
                LEFT -> ObjectAnimator.ofFloat(
                    rootView,
                    "translationX",
                    0f,
                    -SMALL_SHORT_63.toFloat()
                )

                TOP -> ObjectAnimator.ofFloat(
                    rootView,
                    "translationY",
                    0f,
                    -SMALL_SHORT_63.toFloat()
                )

                RIGHT -> ObjectAnimator.ofFloat(
                    rootView,
                    "translationX",
                    0f, SMALL_SHORT_63.toFloat()
                )

                BOTTOM -> ObjectAnimator.ofFloat(
                    rootView,
                    "translationY",
                    0f, SMALL_SHORT_63.toFloat()
                )
            }
        }

        anim.duration = ANIM_DURATION_SHORT
        anim.interpolator = AccelerateDecelerateInterpolator()
        // 为动画添加完成回调
        anim.doOnStart {
            isAutoAnimPlaying = true
        }
        anim.doOnEnd {
            launch {
                logTagV(TAG, "anim.doOnEnd")
                if (direction == COME_IN) {
                    delay(100)
                    isAutoAnimPlaying = false
                    return@launch
                }
                rootView?.visibility = View.GONE
                windowManager.removeView(rootView)
                isSmallCircleView = false

                delay(100)

                // 设置从小变大的大 窗口时候的信息
                when (nowEdge) {
                    LEFT -> {
                        xOffSet = 10
                        yOffset = yOffset
                        windowWidth = BIG_LONG_416
                        windowHeight = BIG_SHORT_105
                        layoutId = saveLayoutId
                    }

                    TOP -> {
                        xOffSet = xOffSet
                        yOffset = 10
                        windowWidth = BIG_LONG_416
                        windowHeight = BIG_SHORT_105
                        layoutId = saveLayoutId
                    }

                    RIGHT -> {
                        xOffSet = getScreenWidth() - BIG_LONG_416 - 10
                        yOffset = yOffset
                        windowWidth = BIG_LONG_416
                        windowHeight = BIG_SHORT_105
                        layoutId = saveLayoutId
                    }

                    BOTTOM -> {
                        xOffSet = xOffSet
                        yOffset = getScreenHeight() - BIG_SHORT_105 - 10
                        windowWidth = BIG_LONG_416
                        windowHeight = BIG_SHORT_105
                        layoutId = saveLayoutId
                    }
                }

                createWindowView()
                reCreateView()
                bigMainViewAnim(nowEdge, COME_IN)
            }
        }
        anim.start()
    }


    private enum class Edge {
        LEFT, TOP, RIGHT, BOTTOM
    }

    abstract fun reCreateView()
}