package com.czur.starry.device.localmeetingrecord.monitor

import android.media.MediaCodec
import android.media.MediaFormat
import android.media.MediaMuxer
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message
import android.os.Process.THREAD_PRIORITY_DISPLAY
import android.text.TextUtils
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.localmeetingrecord.Config
import com.czur.starry.device.localmeetingrecord.RecordState
import com.czur.starry.device.localmeetingrecord.mdoel.RecordConfigModel
import java.io.IOException
import java.util.concurrent.ArrayBlockingQueue


/**
 * 混合音视频线程
 */
class MuxerManager {

    companion object {
        private const val TAG = "MediaMuxerRunnable"

        const val TRACK_VIDEO = 1
        const val TRACK_AUDIO = 2
        private var sInstance: MuxerManager? = null
        private const val MSG_STOP_MUXER = 1
        private const val MSG_SET_NEW_FILE_PATH = 2
        private const val MSG_SET_FORMAT = 3
        private const val MSG_PAUSE_MUXER = 6
        private const val MSG_RELEASE_MANAGER = 4
        val instance: MuxerManager
            get() {
                if (sInstance == null) {
                    synchronized(MuxerManager::class.java) {
                        if (sInstance == null) {
                            sInstance = MuxerManager()
                        }
                    }
                }
                return sInstance!!
            }

        // 使用回调接口代替Flow
        interface OnMixerStartListener {
            fun onMixerStarted()
        }
    }

    private var frameDataQueue: ArrayBlockingQueue<MuxerData>? = null
    private lateinit var muxerThread: Thread

    private val mHandlerThread: HandlerThread =
        HandlerThread("muxerThread", THREAD_PRIORITY_DISPLAY)
    private var mediaMuxer: MediaMuxer? = null
    private var videoTrackIndex = -1
    private var audioTrackIndex = -1

    var videoCodec: MediaCodec? = null
    var audioCodec: MediaCodec? = null

    //混合音视频的线程开启状态
    @Volatile
    public var isMuxerStart = false

    var isStoppingRecord = false

    // 混合器启动监听器
    private var mixerStartListener: OnMixerStartListener? = null

    /**
     * 设置混合器启动监听器
     */
    fun setOnMixerStartListener(listener: OnMixerStartListener) {
        this.mixerStartListener = listener
    }

    /**
     * 移除混合器启动监听器
     */
    fun removeOnMixerStartListener() {
        this.mixerStartListener = null
    }

    private var filePath: String? = null

    private var mHandler: MuxerHandler?

    private var recordConfig = RecordConfigModel()

    init {
        mHandlerThread.start()
        mHandler = MuxerHandler(mHandlerThread.looper)
        frameDataQueue = ArrayBlockingQueue(128)
    }

    private var mediaCodecManager: MediaCodecManager = MediaCodecManager()


    internal inner class MuxerHandler(looper: Looper) : Handler(looper) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            <EMAIL>(msg)
        }
    }

    private fun handleMessage(msg: Message) {
        when (msg.what) {
            MSG_SET_NEW_FILE_PATH -> {
                filePath = msg.obj as String
                readyStart()
            }

            MSG_SET_FORMAT -> addTrackIndex(msg.arg1, msg.obj as MediaFormat)
            MSG_RELEASE_MANAGER -> releaseManager()
            MSG_PAUSE_MUXER -> {}
            else -> {}
        }
    }

    /**
     * 重新开始新视频录制
     *
     * @param filePath
     */
    fun reStartMuxer(
        filePath: String,
        recordConfig: RecordConfigModel
    ) {
        this.recordConfig = recordConfig
        mHandler!!.obtainMessage(MSG_SET_NEW_FILE_PATH, filePath)
            .sendToTarget()
    }

    /**
     * 暂停视频录制
     */
    fun pauseMuxer() {
        mediaCodecManager.pauseMediaCodec()
    }

    fun resumeMuxer() {
        mediaCodecManager.resumeMediaCodec()
    }

    /**
     * 结束当前视频录制
     */
    fun stopMediaManager() {
        isMuxerStart = false
        isStoppingRecord = true
        mHandler!!.obtainMessage(MSG_RELEASE_MANAGER).sendToTarget()
    }

    /**
     * 发送添加track事件
     *
     * @param index
     * @param mediaFormat
     */
    fun sendAddTrack(index: Int, mediaFormat: MediaFormat?) {
        mHandler!!.obtainMessage(MSG_SET_FORMAT, index, 0, mediaFormat).sendToTarget()
    }

    fun addVideoFrameData(data: ByteArray?) {
        if (mediaMuxer != null) mediaCodecManager.addFrameData(data!!)
    }

    fun addAudioFrameData(data: ByteArray, audioMixerSource: Int) {
        if (mediaMuxer != null) mediaCodecManager.addAudioFrameData(data, audioMixerSource)
    }

    /**
     * 设置更换文件
     * create at 2017/3/22 18:06
     */
    private fun readyStart() {
        if (TextUtils.isEmpty(filePath)) return
        try {
            readyStart(filePath, recordConfig.enableTimeWaterMark)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    @Throws(IOException::class)
    private fun readyStart(filePath: String?, enableTimeWaterMark: Boolean) {
        logTagD(TAG, "readyStart start path=${filePath} enableTimeWaterMark:${enableTimeWaterMark}")
        isMuxerStart = false
        mediaMuxer = MediaMuxer(filePath!!, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4)
        mediaCodecManager.initCodecManager(
            recordConfig.recordWidth,
            recordConfig.recordHeight, if (Config.IS_PORT) 270 else 0, recordConfig
        )
        mediaCodecManager.startMediaCodec(enableTimeWaterMark)
        logTagD(TAG, "readyStart end ")
    }

    private fun addTrackIndex(index: Int, mediaFormat: MediaFormat) {
        /*轨迹改变之后,重启混合器*/
        if (isMuxerStart) {
            return
        }
        try {
            mediaMuxer?.let {
                when (index) {
                    TRACK_VIDEO -> {
                        videoTrackIndex = it.addTrack(mediaFormat)
                        logTagD(TAG, "添加轨道-视频(${videoTrackIndex}):${mediaFormat}")
                    }

                    TRACK_AUDIO -> {
                        audioTrackIndex = it.addTrack(mediaFormat)
                        logTagD(TAG, "添加轨道-音频(${audioTrackIndex}):${mediaFormat}")
                    }
                }
//                if (!isMuxerStart && audioTrackIndex != -1 && videoTrackIndex != -1) {
                if (!isMuxerStart && audioTrackFormat() != -1 && videoTrackIndex != -1) {
                    logTagD(TAG, "混合开始")
                    // 发送广播
                    it.start()
                    isMuxerStart = true

                    // 直接在主线程上调用回调函数
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        logTagD(TAG, "混合开始了--通知监听器 ${mixerStartListener}")
                        mixerStartListener?.onMixerStarted()
                        logTagD(TAG, "混合开始了--通知完成")
                    }

                    logTagD(TAG, "混合开始了")
//                    startMuxerThread()
                }
            }
        } catch (e: Exception) {
            logTagE(TAG, "addTrack 异常:", tr = e)
        }
    }

    fun writeSampleData(data: MuxerData?) {
        if (!isMuxerStart) {
            when (data?.trackIndex) {
                TRACK_VIDEO -> {
                    videoCodec?.releaseOutputBuffer(data.bufferIndex, false)
                }

                TRACK_AUDIO -> {
                    audioCodec?.releaseOutputBuffer(data.bufferIndex, false)
                }
            }
            return
        }

        try {

            when (data?.trackIndex) {
                TRACK_VIDEO -> {
//                            val outData = ByteArray(data.byteBuf?.remaining()!!)
//                            data.byteBuf?.get(outData)
//                            // 数据写入文件
//                            outputStream?.write(outData)
//                            outputStream?.flush()
//
//                            data.byteBuf?.position(newBufferInfo.offset)
                    mediaMuxer!!.writeSampleData(
                        videoTrackIndex,
                        data.byteBuf!!,
                        data.bufferInfo!!
                    )

                    videoCodec?.releaseOutputBuffer(data.bufferIndex, false)
                }

                TRACK_AUDIO -> {
                    mediaMuxer!!.writeSampleData(
                        audioTrackIndex,
                        data.byteBuf!!,
                        data.bufferInfo!!
                    )

                    audioCodec?.releaseOutputBuffer(data.bufferIndex, false)
                }
            }
            mediaCodecManager.releaseMuxerData(data)
        } catch (e: Exception) {
            logTagE(TAG, "写入混合数据失败!", tr = e)
        }
    }

    /**
     * 关闭mediaMuxer
     * create at 2017/3/22 17:59
     */
    private fun releaseMuxer() {
        logTagD(TAG, "releaseMuxer start $mediaMuxer")
        if (mediaMuxer != null) {
            try {
                mediaMuxer!!.stop()
                mediaMuxer!!.release()
                audioTrackIndex = -1
                videoTrackIndex = -1
                muxerThread.interrupt()
            } catch (e: Exception) {
                logTagE(TAG, "mediaMuxer.release() 异常", tr = e)
            }
            mediaMuxer = null
            mHandler!!.removeCallbacksAndMessages(null)
        }
    }

    private fun releaseManager() {
        logTagD(TAG, "releaseManager: start")
        releaseMuxer()
        resetMediaCodeManager(true)
        // 移除监听器以避免内存泄漏
        mixerStartListener = null
        sInstance = null
        mHandlerThread.quitSafely()
        audioTrackIndex = -1
        videoTrackIndex = -1
        mHandler = null
        frameDataQueue?.clear()
        frameDataQueue = null
        logTagD(TAG, "releaseManager: end")
    }

    private fun resetMediaCodeManager(release: Boolean = false) {
        mediaCodecManager.releaseManager()
        if (!release) {
            mediaCodecManager = MediaCodecManager()
        }
    }

    private fun audioTrackFormat(): Int { // 在任何声音都不录制的时候,直接返回"已添加音轨的值"来方便后续逻辑的判断
        return if (recordConfig.recordAudio || recordConfig.recordSubmixAudio) {
            audioTrackIndex
        } else {
            TRACK_AUDIO
        }
    }

    fun setRecStatus(recStatus: RecordState?) {
        mediaCodecManager.recStatus = recStatus
    }
}