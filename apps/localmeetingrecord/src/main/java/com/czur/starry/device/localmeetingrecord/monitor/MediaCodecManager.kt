package com.czur.starry.device.localmeetingrecord.monitor

import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.media.SystemManager
import android.os.Handler
import android.os.HandlerThread
import android.os.Process.THREAD_PRIORITY_DISPLAY
import android.os.Process.setThreadPriority
import android.util.Log
import android.view.Surface
import androidx.core.util.Pools
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.SettingHandler
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.localmeetingrecord.Config
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_SAMPLE_RATE
import com.czur.starry.device.localmeetingrecord.Config.DEF_ENABLE_TIME_WATERMARK
import com.czur.starry.device.localmeetingrecord.Config.TIME_WATER_MARK_OFFSET_X
import com.czur.starry.device.localmeetingrecord.Config.TIME_WATER_MARK_OFFSET_Y
import com.czur.starry.device.localmeetingrecord.Config.TIME_WATER_MARK_PATTERN
import com.czur.starry.device.localmeetingrecord.Config.TIME_WATER_MARK_PATTERN_EN
import com.czur.starry.device.localmeetingrecord.RecordModel
import com.czur.starry.device.localmeetingrecord.RecordState
import com.czur.starry.device.localmeetingrecord.jni.YuvOsdUtils
import com.czur.starry.device.localmeetingrecord.mdoel.RecordConfigModel
import com.czur.starry.device.localmeetingrecord.monitor.MuxerManager.Companion.TRACK_AUDIO
import com.czur.starry.device.localmeetingrecord.monitor.MuxerManager.Companion.TRACK_VIDEO
import com.czur.starry.device.localmeetingrecord.monitor.audio.AudioMixer
import com.czur.starry.device.localmeetingrecord.monitor.audio.AudioMixer.Companion.AUDIO_SOURCE_MIC
import com.czur.starry.device.localmeetingrecord.utils.CodecType
import com.czur.starry.device.localmeetingrecord.utils.createCodec
import com.czur.starry.device.localmeetingrecord.utils.handleOutputBuffer
import kotlinx.coroutines.runBlocking
import java.nio.ByteBuffer
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ArrayBlockingQueue
import kotlin.concurrent.thread


internal class MediaCodecManager {
    companion object {
        private const val TAG = "MediaCodecManager"
    }

    var recStatus: RecordState? = RecordState.STOP

    private var frameBytes: ArrayBlockingQueue<ByteArray>? = null

    private var videoCodec: MediaCodec? = null
    private var audioCodec: MediaCodec? = null

    private var mColorFormat = 0
    private var videoFormat: MediaFormat? = null
    private var audioFormat: MediaFormat? = null

    @Volatile
    private var isStart = false
    private var needAddEndVideo = false
    private var needAddEndAudio = false

    @Volatile
    private var isPause = false
    private val isAddOSD = true
    private var dstWidth = 0
    private var dstHeight = 0

    private val scheduleHandlerThread: HandlerThread =
        HandlerThread("ScheduleThread${System.currentTimeMillis()}")
    private val scheduleHandler: Handler
//
//    private val videoHandlerThread: HandlerThread =
//        HandlerThread("VideoCodecThread${System.currentTimeMillis()}")
//    private val videoHandler: Handler       // 同时负责视频编码和调度
//
//    private val audioHandlerThread: HandlerThread =
//        HandlerThread("AudioCodecThread${System.currentTimeMillis()}")
//    private val audioHandler: Handler

    private var mFormat: SimpleDateFormat? = null
    private var mMuxerManager: MuxerManager? = null
    private var isInitCodec = false
    private var isFlush = false
    private var lastPauseTime: Long = -1 //上次暂停时间
    private var isHasKeyFrame = false
    private var rotation = 0
    private var mPools: Pools.SimplePool<MuxerData>? = null
    var outData: ByteArray? = null
    private var hasConfigFrame = false //
    private var hasKeyFrame = false //

    // 是否添加水印
    var addTimeWaterStamp: Boolean = DEF_ENABLE_TIME_WATERMARK
    private lateinit var recordConfigModel: RecordConfigModel

    // 混音器
    private lateinit var audioMixer: AudioMixer

    var isVideoFormatChanged = false
    var isAudioFormatChanged = false
    private lateinit var videoInputCodecThread: Thread
    private lateinit var videoOutputCodecThread: Thread
    private lateinit var audioInputCodecThread: Thread
    private lateinit var audioOutputCodecThread: Thread

    var videoHasFirstIFrame = false // 是否已经有了第一帧I帧
    var audioHasCodecOutputData = false // 是否已经出现audioData

    private val ptsManager = PtsManager()


    init {
        scheduleHandlerThread.start()
        scheduleHandler = Handler(scheduleHandlerThread.looper)
        if (!ptsManager.isRunning) {
            ptsManager.startNewRecord()
        }
    }

    /**
     * 初始化编码器
     *
     * @param dstWidth
     * @param dstHeight
     */
    fun initCodecManager(
        dstWidth: Int,
        dstHeight: Int,
        rotation: Int,
        recordConfigModel: RecordConfigModel = RecordConfigModel()
    ) {
        this.recordConfigModel = recordConfigModel
        isFlush = false

        if (!isInitCodec) {
            logTagI(TAG, "initCodecManager:size{${dstWidth}*${dstHeight}} rotation=$rotation")
            scheduleHandler.post {
                mMuxerManager = MuxerManager.instance
                frameBytes = ArrayBlockingQueue(128)
                // 初始化混音器
                audioMixer = AudioMixer(
                    sourceType = recordConfigModel.audioMixerSource,
                    mixerType = Config.AUDIO_MIX_TYPE
                )
                mPools = Pools.SynchronizedPool(256) //缓存对象池
                <EMAIL> = dstWidth
                <EMAIL> = dstHeight
                <EMAIL> = rotation
                prepare()
                //                String pattern = "yyyy年MM月dd日 HH:mm:ss";//日期格式 年月日
                val pattern = when (SettingHandler.czurLang) {
                    SettingHandler.CZURLang.CN -> TIME_WATER_MARK_PATTERN
                    SettingHandler.CZURLang.EN -> TIME_WATER_MARK_PATTERN_EN
                    SettingHandler.CZURLang.TW -> TIME_WATER_MARK_PATTERN
                    SettingHandler.CZURLang.JP -> TIME_WATER_MARK_PATTERN
                    SettingHandler.CZURLang.RU -> TIME_WATER_MARK_PATTERN
                    SettingHandler.CZURLang.IT -> TIME_WATER_MARK_PATTERN
                    SettingHandler.CZURLang.DE -> TIME_WATER_MARK_PATTERN
                    SettingHandler.CZURLang.KO -> TIME_WATER_MARK_PATTERN
                    SettingHandler.CZURLang.FR -> TIME_WATER_MARK_PATTERN
                    SettingHandler.CZURLang.ES -> TIME_WATER_MARK_PATTERN
                }
                mFormat = SimpleDateFormat(pattern, Locale.ROOT)
                YuvOsdUtils.initOsd(
                    TIME_WATER_MARK_OFFSET_X,
                    TIME_WATER_MARK_OFFSET_Y,
                    TIME_WATER_MARK_PATTERN.length,
                    dstWidth,
                    dstHeight,
                    rotation
                )
                isInitCodec = true
            }
        }
    }

    /**
     * 开始编码器
     */
    fun startMediaCodec(enableTimeWaterMark: Boolean) {
        if (isStart) {
            logTagI(TAG, "startMediaCodec: was started")
            return
        }
        this.addTimeWaterStamp = enableTimeWaterMark
        scheduleHandler.post { start() }
    }

    /**
     * 暂停录制
     */
    fun pauseMediaCodec() {
        ptsManager.pause()
        if (!isStart || isPause) {
            logTagI(TAG, "MediaCodec: isn't started")
            return
        }
        isPause = true
        frameBytes!!.clear()
        audioMixer.clearAudioFrameData()
    }


    /**
     * 继续录制
     */
    fun resumeMediaCodec() {
        ptsManager.resume()
        if (!isStart || !isPause) {
            logTagI(TAG, "MediaCodec: was started")
            return
        }
        isPause = false
    }

    fun releaseManager() {
        logTagI(TAG, "releaseManager")
        scheduleHandler.post {

            logTagI(TAG, "执行释放操作")
            if (videoCodec != null || audioCodec != null) {
                stopMediaCodec()
            }
            doWithoutCatch {
                mPools = null
                outData = null
                ptsManager.reset()
                videoInputCodecThread.interrupt()
                videoOutputCodecThread.interrupt()
                audioInputCodecThread.interrupt()
                audioOutputCodecThread.interrupt()
                audioMixer.addAudioFrameData(ByteArray(123), AUDIO_SOURCE_MIC)
                frameBytes?.offer(ByteArray(123))
                frameBytes?.clear()
                frameBytes = null
                audioMixer.clearAudioFrameData()
                recStatus = RecordState.STOP
                YuvOsdUtils.releaseOsd()
            }
            scheduleHandler.removeCallbacksAndMessages(null)
            scheduleHandlerThread.quitSafely()
            logTagI(TAG, "执行释放操作完成")
        }

    }

    fun addFrameData(data: ByteArray) {
        if (isStart && !isPause && recordConfigModel.recordMode != RecordModel.SCREEN_MODE) {
            val copiedData = data.copyOf()
            val isOffer = frameBytes!!.offer(copiedData)

            if (!isOffer) {
                frameBytes!!.poll()
                frameBytes!!.offer(copiedData)
            }
        }
    }

    fun addAudioFrameData(data: ByteArray, audioMixerSource: Int) {
        if (isStart && !isPause) {
            audioMixer.addAudioFrameData(data, audioMixerSource)
        }
    }

    /**
     * 准备一些需要的参数
     *
     *
     * YV12: YYYYYYYY VV UU    =>YUV420P
     * NV12: YYYYYYYY UVUV     =>YUV420SP
     * NV21: YYYYYYYY VUVU     =>YUV420SP
     * create at 2017/3/22 18:13
     */
    private fun prepare() {
        mColorFormat = if (recordConfigModel.recordMode == RecordModel.VIDEO_MODE) {
            MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Flexible
        } else if (recordConfigModel.recordMode == RecordModel.SCREEN_MODE) {
            MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface
        } else {
            MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Flexible
        }
        val videoW = if (rotation == 90 || rotation == 270) dstHeight else dstWidth
        val videoH = if (rotation == 90 || rotation == 270) dstWidth else dstHeight
        videoFormat = MediaFormat.createVideoFormat(
            CodecType.VIDEO.mimeType,  //注意这里旋转后有一个大坑，就是要交换mHeight，和mWidth的位置。否则录制出来的视频时花屏的。
            videoW, videoH
        )
        val frameRate = 15 // 15fps
        val compressRatio = 256
        val bitRate = dstWidth * dstHeight * 3 * 8 * frameRate / compressRatio
        videoFormat?.let {
            it.setInteger(MediaFormat.KEY_BIT_RATE, bitRate)
            it.setInteger(MediaFormat.KEY_FRAME_RATE, frameRate)
            it.setInteger(MediaFormat.KEY_COLOR_FORMAT, mColorFormat)
            it.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1)
        }

        logTagD(TAG, "prepare format: $videoFormat")

        // 音频
        audioFormat =
            MediaFormat.createAudioFormat(
                CodecType.AUDIO.mimeType,
                AUDIO_SAMPLE_RATE,
                Config.AUDIO_MIX_TYPE.channelCount
            )
                .apply {
                    setInteger(MediaFormat.KEY_BIT_RATE, Config.AUDIO_ENCODING_BIT_RATE)
                    setInteger(
                        MediaFormat.KEY_AAC_PROFILE,
                        MediaCodecInfo.CodecProfileLevel.AACObjectLC
                    )
                    setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 8192)
                }
    }

    private fun start() {
        if (!isInitCodec) throw RuntimeException("initCodec is false,please call initCodecManager() before")
        if (isStart) {
            logTagI(TAG, "startMediaCodec: was started")
            return
        }
        isStart = true
        logTagI(TAG, "startMediaCodec: starting")

        try {

            if (recordConfigModel.recordAudio || recordConfigModel.recordSubmixAudio) {
                initAudioCodec()
                mMuxerManager?.audioCodec = audioCodec

                audioInputCodecThread = thread(true, name = "audioInputCodec") {
                    setThreadPriority(THREAD_PRIORITY_DISPLAY) // 设置线程优先级

                    audioInputCodec()
                }
                audioOutputCodecThread = thread(true, name = "audioOutputCodec") {
                    setThreadPriority(THREAD_PRIORITY_DISPLAY) // 设置线程优先级

                    audioOutputCodec()
                }

            }



            initVideoCodec()
            mMuxerManager?.videoCodec = videoCodec
            if (recordConfigModel.recordMode != RecordModel.SCREEN_MODE) {
                videoInputCodecThread = thread(true, name = "videoInputCodec") {
                    setThreadPriority(THREAD_PRIORITY_DISPLAY) // 设置线程优先级
                    videoInputCodec()
                }
            }
            videoOutputCodecThread = thread(true, name = "videoOutputCodec") {
                setThreadPriority(THREAD_PRIORITY_DISPLAY) // 设置线程优先级
                videoOutputCodec()
            }


        } catch (e: Exception) {
            logTagW(TAG, "初始化失败", tr = e)
            return
        }

    }

    private fun videoInputCodec() {
        while (isStart && !MuxerManager.instance.isStoppingRecord) {
            if (!isPause) {
                var dequeueInputBufferIndex = -1
                try {
                    dequeueInputBufferIndex = videoCodec!!.dequeueInputBuffer(1000)
                } catch (e: Exception) {
                    Log.e(TAG, "initVideoCodec: dequeueInputBuffer faild -- ${e.message}")
                }
                if (dequeueInputBufferIndex >= 0) {
                    val data: ByteArray? = try {
                        frameBytes?.take()
                    } catch (e: InterruptedException) { // 捕获中断异常
                        Log.d(TAG, "videoInputCodec thread interrupted")
                        break // 或者 break，跳出循环
                    } catch (e: Throwable) {
                        Log.e(TAG, "Error taking frame data: ${e.message}")
                        e.printStackTrace()
                        break
                    }

                    if (data?.size == 123) {
                        break
                    }

                    if (outData == null || outData?.size != data?.size) { // check size of outData
                        outData = ByteArray(
                            data?.size ?: 0
                        ) // use safe-call to avoid NullPointerException
                    }

                    val dataReady = outData ?: break


                    try {
                        val inputBuffer = videoCodec?.getInputBuffer(dequeueInputBufferIndex)

                        val date = if (addTimeWaterStamp) mFormat?.format(Date()) else ""
                        YuvOsdUtils.addOsd(data, outData, date)


                        if (inputBuffer?.capacity()!! < dataReady.size) {
                            throw RuntimeException("InputBuffer not large enough for data")
                        }


                        inputBuffer.clear()

                        if (data != null) {
                            inputBuffer.put(outData)
                        }

                        Thread.sleep(50)
                        val pts = System.nanoTime() / 1000
                        if (isStart && !isPause) {
                            videoCodec?.queueInputBuffer(
                                dequeueInputBufferIndex,
                                0,
                                outData?.size!!,
                                pts,
                                0
                            )
                        } else {
                            inputBuffer.clear()
                            logTagV(TAG, "codec结束了,清理inputbuffer")
                        }

                    } catch (e: Throwable) {
                        e.printStackTrace()
                        Log.e(TAG, "onVideoInputBufferAvailable--error--${e.message}")
                    }
                }
            }
        }
        logTagV(TAG, "清理videoInputCodec")
    }

    private fun videoOutputCodec() {
        while (isStart && !MuxerManager.instance.isStoppingRecord) {
            val videoBufferInfo = MediaCodec.BufferInfo()
            //************OUTPUT*****************//
            videoCodec?.handleOutputBuffer(videoBufferInfo, 1000, {
                // audio format changed
                if (!isVideoFormatChanged) {
                    val newFormat = videoCodec?.outputFormat
                    Log.v(TAG, "添加轨道  $newFormat")
                    mMuxerManager?.sendAddTrack(MuxerManager.TRACK_VIDEO, newFormat)
                    isVideoFormatChanged = true
                }
            }, {
                doWithoutCatch {
                    if (isPause) {
                        videoCodec?.releaseOutputBuffer(it, false)
                        return@handleOutputBuffer
                    }

                    val outputBuffer = videoCodec?.getOutputBuffer(it)
                    if (videoCodec == null) {
                        videoCodec?.releaseOutputBuffer(it, false)
                        return@handleOutputBuffer
                    }

                    if (MuxerManager.instance.isStoppingRecord) {
                        videoCodec?.releaseOutputBuffer(it, false)
                        return@handleOutputBuffer
                    }


                    if (videoBufferInfo.flags == MediaCodec.BUFFER_FLAG_CODEC_CONFIG) {
                        videoCodec?.releaseOutputBuffer(it, false)
                        return@handleOutputBuffer
                    }

                    if (recordConfigModel.recordAudio || recordConfigModel.recordSubmixAudio) {
                        // 不录制音频的时候不需要等待
                        if (!audioHasCodecOutputData) {
                            // 等待音频的数据加进来,音频是靠判断recStatus == RecordState.REC
                            // android12上在录屏时候同时打开播放器会很卡,set recStatus的时间会很长,所以需要等待音频的判断
                            videoCodec?.releaseOutputBuffer(it, false)
                            return@handleOutputBuffer
                        }
                    } else {
                        if (recStatus != RecordState.REC) {
                            videoCodec?.releaseOutputBuffer(it, false)
                            return@handleOutputBuffer
                        }
                    }

                    // 开始编程以后,等一个i帧,再正式编
                    if (!videoHasFirstIFrame && videoBufferInfo.flags == MediaCodec.BUFFER_FLAG_KEY_FRAME) {
                        videoHasFirstIFrame = true
                    } else if (!videoHasFirstIFrame) {
                        videoCodec?.releaseOutputBuffer(it, false)
                        return@handleOutputBuffer
                    }

                    val us = System.nanoTime() / 1000
                    if (!ptsManager.isVideoRunning) {

                        ptsManager.setVideoStartTime(us)
                    }
                    val bufferInfo = ptsManager.resetBufferInfoPts(videoBufferInfo, TRACK_VIDEO)
                    if (videoBufferInfo.size > 0) {
                        writeFrame(outputBuffer, bufferInfo, it)
                    } else {
                        videoCodec?.releaseOutputBuffer(it, false)
                    }
                }
            })
        }
        logTagV(TAG, "清理videoOutputCodec")
    }

    private fun audioInputCodec() {
        while (isStart && !MuxerManager.instance.isStoppingRecord) {
            try {
                if (isPause) {
                    Thread.sleep(20)
                    continue
                }

                val dequeueInputBufferIndex = audioCodec!!.dequeueInputBuffer(1000)
                val inputBuffer = audioCodec?.getInputBuffer(dequeueInputBufferIndex) ?: return

                val pop = try {
                    runBlocking {
                        audioMixer.getAudioFrameData()
                    }
                } catch (e: InterruptedException) { // 捕获中断异常
                    Log.d(TAG, "AudioInputCodec thread interrupted")
                    break // 或者 break，跳出循环
                } catch (e: Exception) {
                    Log.e(TAG, "Error getting audio frame data: ${e.message}")
                    if (isPause) {
                        continue
                    } else {
                        break
                    }
                }
                if (pop.size == 123) {
                    break
                } else if (pop.size < 123) {

                }

                // 填入数据
                inputBuffer.clear()
                inputBuffer.limit(pop.size)
                inputBuffer.put(pop)

                val pts = System.nanoTime() / 1000
                // 将buffer还给MediaCodec进行编码
                if (isStart && !isPause) {
                    audioCodec?.queueInputBuffer(
                        dequeueInputBufferIndex,
                        0,
                        pop.size,
                        pts,
                        0
                    )
                } else {
                    inputBuffer.clear()
                }

            } catch (e: Exception) {
                Log.d(TAG, "initAudioCodec: 5555 ${e.message}")

            }


        }

        logTagV(TAG, "清理audioInputCodec")
    }


    private fun audioOutputCodec() {

        while (isStart && !MuxerManager.instance.isStoppingRecord) {
            val audioBufferInfo = MediaCodec.BufferInfo()
            //***********************OUT_PUT*************************//
            audioCodec?.handleOutputBuffer(audioBufferInfo, 1000, {
                // audio format changed
                if (!isAudioFormatChanged) {
                    val newFormat = audioCodec?.outputFormat

                    mMuxerManager?.sendAddTrack(TRACK_AUDIO, newFormat)
                    isAudioFormatChanged = true
                }
            }, {
                val outputBuffer = audioCodec?.getOutputBuffer(it)
                if (isPause) {
                    audioCodec?.releaseOutputBuffer(it, false)
                    return@handleOutputBuffer
                }
                if (MuxerManager.instance.isStoppingRecord) {
                    audioCodec?.releaseOutputBuffer(it, false)
                    return@handleOutputBuffer
                }

                if (audioBufferInfo.size > 122 && !audioHasCodecOutputData) {
                    // 123 是设定的退出size
                    audioHasCodecOutputData = true
                } else if (audioBufferInfo.size <= 122 || !audioHasCodecOutputData) {
                    audioCodec?.releaseOutputBuffer(it, false)
                    return@handleOutputBuffer
                }

                if (!videoHasFirstIFrame) {
                    audioCodec?.releaseOutputBuffer(it, false)
                    return@handleOutputBuffer
                }
                if (!ptsManager.isAudioRunning) {
                    val us = System.nanoTime() / 1000
                    ptsManager.setAudioStartTime(us)
                }
                val bufferInfo = ptsManager.resetBufferInfoPts(audioBufferInfo, TRACK_AUDIO)

                if (audioBufferInfo.size > 0) {
                    val muxerData = mPools?.acquire() ?: MuxerData()
                    muxerData.trackIndex = MuxerManager.TRACK_AUDIO
                    muxerData.bufferInfo = bufferInfo
                    muxerData.byteBuf = outputBuffer
                    muxerData.bufferIndex = it
                    mMuxerManager?.writeSampleData(muxerData)
                } else {
                    audioCodec?.releaseOutputBuffer(it, false)
                }
            })
        }
        logTagV(TAG, "清理audioOutputCodec")
    }


    private fun initVideoCodec() {
        Log.v(TAG, "initVideoCodec")

        videoCodec = createCodec(
            CodecType.VIDEO, videoFormat!!
        )

        var createInputSurface: Surface? = null
        if (recordConfigModel.recordMode == RecordModel.SCREEN_MODE) {
            createInputSurface = videoCodec!!.createInputSurface()
        }

        videoCodec!!.start()

        if (recordConfigModel.recordMode == RecordModel.SCREEN_MODE) {
            recordConfigModel.virtualDisplay!!.surface = createInputSurface
        }
        logTagV(TAG, "initCodec---Completed")
    }


    private fun initAudioCodec() {
        Log.i(TAG, "initAudioCodec")
        audioCodec = createCodec(
            CodecType.AUDIO, audioFormat!!
        )
        audioCodec!!.start()
    }

    private fun stopMediaCodec() {
        isStart = false
        Thread.sleep(100)
        videoCodec?.let {
            logTagD(TAG, "释放VideoCodec")
            it.flush()
            it.stop()
            it.release()
            videoCodec = null
        }

        audioCodec?.let {
            logTagD(TAG, "释放AudioCodec")
            it.stop()
            it.release()
            audioCodec = null
        }
        isPause = true
        logTagI(TAG, "stopMediaCodec video")
    }

    fun releaseMuxerData(data: MuxerData?) {
        if (mPools != null && data != null) {
            data.byteBuf = null
            data.bufferInfo = null
            data.bufferIndex = -999
            data.codec = null
            mPools?.release(data)
        }
    }

    // 该函数用来写入相应的帧
    private fun writeFrame(
        outputBuffer: ByteBuffer?,
        info: MediaCodec.BufferInfo,
        index: Int
    ): Boolean {
        if (outputBuffer == null) {
            return false
        }
        var muxerData: MuxerData? = mPools?.acquire()
        if (muxerData == null) {
            muxerData = MuxerData()
        }
        muxerData.trackIndex = TRACK_VIDEO
        muxerData.bufferInfo = info
        muxerData.byteBuf = outputBuffer
        muxerData.bufferIndex = index
        mMuxerManager?.writeSampleData(muxerData)
        return true
    }


}