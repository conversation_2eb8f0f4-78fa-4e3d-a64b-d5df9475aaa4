package com.czur.starry.device.smallroundscreen

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.sampleWithTrailing
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * Created by 陈丰尧 on 2025/5/21
 */
private const val TAG = "AnimationViewModel"
class AnimationViewModel(application: Application) : AndroidViewModel(application) {

    enum class USBEvent{
        IN,
        OUT
    }

    private val _usbEventFlow = MutableSharedFlow<USBEvent>()
    val usbEventFlow = _usbEventFlow.sampleWithTrailing(ONE_SECOND * 3).distinctUntilChanged()


    fun onUSBEventChange(event: USBEvent){
        launch {
            logTagD(TAG, "emit:$event")
            _usbEventFlow.emit(event)
        }
    }

}