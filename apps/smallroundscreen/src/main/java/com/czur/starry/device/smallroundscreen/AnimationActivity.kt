package com.czur.starry.device.smallroundscreen

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.RemoteException
import android.provider.Settings
import android.view.KeyEvent
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.KEY_LAUNCHER_BOOT_COMPLETE
import com.czur.starry.device.baselib.common.KEY_STARTUP_COMPLETE
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.getIntSystemProp
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.smallroundscreen.animation.AnimationPlayerImpl
import com.czur.starry.device.smallroundscreen.animation.BaseHdmiAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.BaseVideoAnimation
import com.czur.starry.device.smallroundscreen.animation.HdmiByomBgAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.HdmiByomWordAppearAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.HdmiByomWordDisappearAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.HdmiFlashingAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.HdmiInExtractAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.HdmiInInsertAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.HdmiNormalAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.HdmiOutNormalAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.HdmiOutUnconnectedAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.UsbByomBgAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.UsbByomWordAppearAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.UsbByomWordDisappearAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.UsbExtractAnimationDrawable
import com.czur.starry.device.smallroundscreen.animation.UsbInsertAnimationDrawable
import com.czur.starry.device.smallroundscreen.databinding.ActivityAnimationBinding
import com.czur.starry.device.smallroundscreen.share.ShareViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.withContext
import rockchip.hardware.hdmi.V1_0.IHdmi
import rockchip.hardware.hdmi.V1_0.IHdmiCallback

class AnimationActivity : CZViewBindingAty<ActivityAnimationBinding>() {

    enum class HdmiOutStateEnum {
        HDMI_OUT_STATE_CONNECTED,
        HDMI_OUT_STATE_DISCONNECTED
    }

    companion object {
        const val BRIGHTNESS_LOW_LIGHT = 75
        const val BRIGHTNESS_HIGH_LIGHT = 255
        private const val TAG = "AnimationActivity"
        private const val WORD_JUMP_TIME: Long = 2 * ONE_MIN
        private const val SCREEN_OFF_SOON =
            "com.czur.starry.device.smallroundscreen.SCREEN_OFF_SOON"
        private const val WILL_FACTORY_RESET = "android.intent.action.WILL_FACTORY_RESET"
        private const val ACTION_STARTUP_FINISH = "com.czur.starry.device.launcher.ACTION_STARTUP_FINISH"
    }

    var isStop: Boolean = false
    var delaySingleAnimation: Boolean = true
    private var isFirstLaunch = true
    private var isLaunchSetFinish = false

    private val bgPlayer1 = AnimationPlayerImpl()
    private val wordPlayer1 = AnimationPlayerImpl()
    private val bgPlayer2 = AnimationPlayerImpl()
    private val wordPlayer2 = AnimationPlayerImpl()
    private val singlePlayer = AnimationPlayerImpl()
    private val shareViewModel: ShareViewModel by viewModels()
    private val animViewModel: AnimationViewModel by viewModels()

    private val systemManagerProxy: SystemManagerProxy by lazy { SystemManagerProxy(::onSystemChange) }

    private var hdmiService: IHdmi? = null
    private var hdmiCallback: IHdmiCallback? = null
    private var hdmiOutState: HdmiOutStateEnum? = null

    private var hdmiOutConnectJob: Job? = null
    private var setLowLightJob: Job? = null

    val startUpComplete by lazy {
        getBooleanSystemProp(KEY_STARTUP_COMPLETE, false)
    }
    val launcherComplete by lazy {
        getBooleanSystemProp(KEY_LAUNCHER_BOOT_COMPLETE, false)
    }

    override fun ActivityAnimationBinding.initBindingViews() {
        enableEdgeToEdge()
        supportActionBar?.hide()
    }

    private var byomPeripheralOpened = false // 新增标志位，用于记录 BYOM 外设是否已经开启过
    private var usbPeripheralOpened = false

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        val startFilter = IntentFilter(ACTION_STARTUP_FINISH)
        ContextCompat.registerReceiver(
            this,
            startupFinishReceiver,
            startFilter,
            ContextCompat.RECEIVER_EXPORTED
        )

        registerHDMICallback()

        val filterUsb = IntentFilter().apply {
            addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
            addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        }
        registerReceiver(usbReceiver, filterUsb)

        repeatOnResume {
            startHdmiOutBGAnimation()
        }

        // Listen for BYOM mode changes
        repeatCollectOnResume(shareViewModel.peripheralByomRunningFlow) {
            if (!isLaunchSetFinish) {
                logTagD(TAG, "BYOM外设 $it ，Launch启动设置完成通知")
                return@repeatCollectOnResume
            }
            if (it) {
                logTagD(TAG, "BYOM外设已开启")
                startHdmiByomAnimation()
                byomPeripheralOpened = true // 标记 BYOM 外设已开启
            } else {
                if (byomPeripheralOpened) {
                    byomPeripheralOpened = false
                    logTagD(TAG, "BYOM外设已关闭")
                    bgPlayer2.pause()
                    stopHdmiByomAnimation()
                }
            }
        }

        // Listen for USB peripheral mode changes
        repeatCollectOnResume(shareViewModel.peripheralUSBRunningFlow) {
            if (!isLaunchSetFinish) {
                logTagD(TAG, "USB外设 $it ，Launch启动设置完成通知")
                return@repeatCollectOnResume
            }
            if (it) {
                logTagD(TAG, "USB外设已开启")
                startUsbByomAnimation()
                usbPeripheralOpened = true // 标记 BYOM 外设已开启
            } else {
                if (usbPeripheralOpened) {
                    usbPeripheralOpened = false
                    logTagD(TAG, "USB外设已关闭")
                    bgPlayer2.pause()
                    stopUSBByomAnimation()
                }
            }
        }

        repeatCollectOnResume(animViewModel.usbEventFlow) {
            logTagD(TAG, "USBState:$it")
            when (it) {
                AnimationViewModel.USBEvent.IN -> startUsbInsertAnimation()
                AnimationViewModel.USBEvent.OUT -> startUsbExtractAnimation()
            }
        }
    }

    private val screenOffSoonReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == SCREEN_OFF_SOON) {
                logTagD(TAG, "Received SCREEN_OFF_SOON broadcast")
                // Handle the SCREEN_OFF_SOON action here
            }
        }
    }

    private val startupFinishReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == ACTION_STARTUP_FINISH) {
                logTagD(TAG, "收到Launch启动设置完成通知")
                isLaunchSetFinish = true
                if (shareViewModel.peripheralUSBRunningFlow.value){
                    logTagD(TAG, "USB外设已开启，开始USB BYOM动画")
                    startUsbByomAnimation()
                    usbPeripheralOpened = true // 标记 BYOM 外设已开启
                }
                if (shareViewModel.peripheralByomRunningFlow.value) {
                    logTagD(TAG, "BYOM外设已开启，开始HDMI BYOM动画")
                    startHdmiByomAnimation()
                    byomPeripheralOpened = true // 标记 BYOM 外设已开启
                }
                // 取消对 ACTION_STARTUP_FINISH 的监听
                unregisterReceiver(this)
            }
        }
    }

    private val usbReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            logTagD("UsbDeviceReceiver", "=====${intent?.action}")
            when (intent?.action) {
                UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                    val device = intent.getParcelableExtra<UsbDevice>(UsbManager.EXTRA_DEVICE)
                    device?.let {
                        logTagD(TAG, "USB设备插入: ${it.deviceName}")
                        animViewModel.onUSBEventChange(AnimationViewModel.USBEvent.IN)
                    }
                }

                UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                    val device = intent.getParcelableExtra<UsbDevice>(UsbManager.EXTRA_DEVICE)
                    device?.let {
                        logTagD(TAG, "USB设备拔出: ${it.deviceName}")
                        animViewModel.onUSBEventChange(AnimationViewModel.USBEvent.OUT)
                    }
                }
            }
        }
    }

    private val factoryResetReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == WILL_FACTORY_RESET) {
                // 这里处理恢复出厂设置的逻辑
                logTagD(TAG, "收到恢复出厂设置广播")
                isStop = true
            }
        }
    }

    private fun onSystemChange(
        category: Int,
        eventID: Int,
        para1: Int,
        extend1: String,
        extend2: String
    ) {
        logTagD(
            TAG,
            "onSystemChange category = $category, eventID = $eventID, para1 = $para1, extend1 = $extend1, extend2 = $extend2"
        )
        if (isStop) {
            logTagD(TAG, "onSystemChange isStop")
            return
        }

        if (category == SystemManagerProxy.CALLBACK_CATEGORY_HDMIOUT_STATE) {
            launch {
                //延迟100毫秒 vendor.czur.hdmi.out有返回不准确的时候 怀疑是因为系统还没有完全准备好
                kotlinx.coroutines.delay(100)
                val outNum = getIntSystemProp("vendor.czur.hdmi.out",0)
                logTagD(TAG, "HDMI OUT状态变化, vendor = $outNum")
                hdmiOutState = if (outNum > 0) HdmiOutStateEnum.HDMI_OUT_STATE_CONNECTED else HdmiOutStateEnum.HDMI_OUT_STATE_DISCONNECTED
                if (para1 == 1) {
                    // HDMI OUT插入
                    logTagD(TAG, "HDMI OUT插入")
                    startHdmiOutStateAnimation()
                } else if (para1 == 0) {
                    // HDMI OUT拔出
                    logTagD(TAG, "HDMI OUT拔出")
                    if (hdmiOutState == HdmiOutStateEnum.HDMI_OUT_STATE_DISCONNECTED) {
                        startHdmiOutStateAnimation()
                    }
                }
            }
        }
    }

    /**
     * 注册HDMI的回调
     */
    private fun registerHDMICallback() {
        try {
            hdmiCallback = HDMICallback()
            hdmiService = IHdmi.getService(true)
            hdmiService?.registerListener(this.packageName, hdmiCallback)
        } catch (e: Throwable) {
            hdmiService = null
            hdmiCallback = null
            logTagW(TAG, "注册HDMI回调失败", tr = e)
        }
    }

    private inner class HDMICallback : IHdmiCallback.Stub() {
        override fun onConnect(p0: String?) {
            if (startUpComplete && launcherComplete) {
                logTagD(TAG, "HDMI IN 连接")
                startHdmiInsertAnimation()
            }
        }

        override fun onFormatChange(p0: String?, p1: Int, p2: Int) {
//            logTagD(TAG, "=======小圆屏 HDMI格式变化====")
        }

        override fun onDisconnect(p0: String?) {
            logTagD(TAG, "HDMI IN 断开:$p0")
            startHdmiExtractAnimation()
        }
    }

    /**
     * 取消注册HDMI的监听
     */
    private fun unRegisterHDMICallback() {
        try {
            hdmiService?.unregisterListener(this.packageName, hdmiCallback)
        } catch (e: RemoteException) {
            logTagW(TAG, "unRegisterHDMICallback 失败", tr = e)
        }
    }

    override fun onStop() {
        super.onStop()
        logTagD(TAG, "onStop")
        isStop = true
        delaySingleAnimation = true
        stopFlashingAnimationPeriodically()
        unregisterReceiver(screenOffSoonReceiver)
        unregisterReceiver(factoryResetReceiver)
        hdmiOutConnectJob?.cancel()
        wordPlayer1.stop()
        wordPlayer2.stop()
        binding.animationword1.setImageDrawable(null)
        binding.animationword2.setImageDrawable(null)
    }

    override fun onStart() {
        super.onStart()
        logTagD(TAG, "onStart")
        isStop = false
        if (isFirstLaunch) {
            logTagD(TAG, "Activity 刚被创建，靠onWindowFocusChanged刷新")
        } else {
            logTagD(TAG, "Activity 不是刚被创建，立即执行.")
            getHdmiOutState()
        }

        val filter = IntentFilter(SCREEN_OFF_SOON)
        ContextCompat.registerReceiver(
            this,
            screenOffSoonReceiver,
            filter,
            ContextCompat.RECEIVER_EXPORTED
        )
        // 注册 WILL_FACTORY_RESET 广播
        val willResetFilter = IntentFilter(WILL_FACTORY_RESET)
        ContextCompat.registerReceiver(
            this,
            factoryResetReceiver,
            willResetFilter,
            ContextCompat.RECEIVER_EXPORTED
        )
    }

    private fun getHdmiOutState() {
        val isConnected = systemManagerProxy.getHdmiOutState()
        hdmiOutState = if (isConnected) {
            logTagD(TAG, "HDMI OUT 已连接")
            HdmiOutStateEnum.HDMI_OUT_STATE_CONNECTED
        } else {
            logTagD(TAG, "HDMI OUT 未连接")
            HdmiOutStateEnum.HDMI_OUT_STATE_DISCONNECTED
        }

        if (delaySingleAnimation) {
            val delayTime = if (isConnected) 4000L else 5000L
            launch {
                kotlinx.coroutines.delay(delayTime)
                delaySingleAnimation = false
            }
        }

        startHdmiOutStateAnimation()
    }

    //播放HDMI OUT状态动画
    private fun startHdmiOutStateAnimation() {
        when (hdmiOutState) {
            HdmiOutStateEnum.HDMI_OUT_STATE_CONNECTED -> hdmiOutConnectAnimation()
            HdmiOutStateEnum.HDMI_OUT_STATE_DISCONNECTED -> hdmiOutDisconnectAnimation()
            else -> logTagW(TAG, "Unknown HDMI OUT state")
        }
    }

    private var isFlashingAnimationRunning = false

    private val handler = Handler(Looper.getMainLooper())
    private val flashingAnimationRunnable = object : Runnable {
        override fun run() {
            startFlashingAnimation()
            handler.postDelayed(this, WORD_JUMP_TIME)
        }
    }

    fun startFlashingAnimationPeriodically() {
        if (!isFlashingAnimationRunning) {
            isFlashingAnimationRunning = true
            logTagD(TAG, "添加文字跳动动画任务")
            handler.post(flashingAnimationRunnable)
        } else {
            logTagD(TAG, "已有文字跳动动画任务")
        }
    }

    fun stopFlashingAnimationPeriodically() {
        logTagD(TAG, "移除文字跳动动画任务")
        handler.removeCallbacks(flashingAnimationRunnable)
        isFlashingAnimationRunning = false
    }

    private fun hdmiOutConnectAnimation() {
        startOutNormalAnimation()
        hdmiOutConnectJob = launch {
            kotlinx.coroutines.delay(10 * 1000)
            if (hdmiOutState == HdmiOutStateEnum.HDMI_OUT_STATE_CONNECTED) {
                startFlashingAnimationPeriodically()
            }
        }
    }

    private fun hdmiOutDisconnectAnimation() {
        checkHdmiOutAndSetLowLight()
        startOutUnconnectedAnimation()
        stopFlashingAnimationPeriodically()
    }

    //Hdmi Out 背景动画
    private fun startHdmiOutBGAnimation() {
        if (!isStop) {
            logTagD(TAG, "Hdmi Out 背景动画")
            runOnUiThread {
                val animation = HdmiNormalAnimationDrawable(this, binding.animationbg1)
                bgPlayer1.play(animation)
            }
        }
    }

    //Hdmi Out 文字跳动动画
    private fun startFlashingAnimation() {
        if (!isStop) {
            logTagD(TAG, "Hdmi Out 文字跳动动画")
            runOnUiThread {
                val animation = HdmiFlashingAnimationDrawable(this, binding.animationword1)
                wordPlayer1.play(animation)
            }
        }
    }

    //Hdmi Out 文字已连接动画
    private fun startOutNormalAnimation() {
        if (!isStop) {
            logTagD(TAG, "Hdmi Out 文字已连接动画")
            setSmallRoundScreenLight(true)
            runOnUiThread {
                val animation = HdmiOutNormalAnimationDrawable(this, binding.animationword1)
                wordPlayer1.play(animation)
            }
        }
    }

    //Hdmi Out 文字未连接动画
    private fun startOutUnconnectedAnimation() {
        if (!isStop) {
            logTagD(TAG, "Hdmi Out 文字未连接动画")
            runOnUiThread {
                val animation = HdmiOutUnconnectedAnimationDrawable(this, binding.animationword1)
                wordPlayer1.play(animation)
            }
        }
    }

    // Hdmi In 插入动画
    private fun startHdmiInsertAnimation() {
        if (!isStop && !delaySingleAnimation) {
            logTagD(TAG, "Hdmi In 插入动画")
            setSmallRoundScreenLight(true)
            checkHdmiOutAndSetLowLight()
            runOnUiThread {
                val animation = HdmiInInsertAnimationDrawable(this, binding.singleAnimation)
                singlePlayer.play(animation)
            }
        }
    }

    // Hdmi In 拔出动画
    private fun startHdmiExtractAnimation() {
        if (!isStop && !delaySingleAnimation) {
            logTagD(TAG, "Hdmi In 拔出动画")
            setSmallRoundScreenLight(true)
            checkHdmiOutAndSetLowLight()
            runOnUiThread {
                val animation = HdmiInExtractAnimationDrawable(this, binding.singleAnimation)
                singlePlayer.play(animation)
            }
        }
    }

    // USB 插入动画
    private fun startUsbInsertAnimation() {
        if (!isStop && !delaySingleAnimation) {
            logTagD(TAG, "USB 插入动画")
            setSmallRoundScreenLight(true)
            checkHdmiOutAndSetLowLight()
            runOnUiThread {
                val animation = UsbInsertAnimationDrawable(this, binding.singleAnimation)
                singlePlayer.play(animation)
            }
        }
    }

    // USB 拔出动画
    private fun startUsbExtractAnimation() {
        if (!isStop && !delaySingleAnimation) {
            logTagD(TAG, "USB 拔出动画")
            setSmallRoundScreenLight(true)
            checkHdmiOutAndSetLowLight()
            runOnUiThread {
                val animation = UsbExtractAnimationDrawable(this, binding.singleAnimation)
                singlePlayer.play(animation)
            }
        }
    }

    //Byom - Hdmi 状态中动画
    private fun startHdmiByomAnimation() {
        logTagD(TAG, "Byom - Hdmi 状态中动画")
        setSmallRoundScreenLight(true)
        playOverlayAnimation(
            bgDrawable = HdmiByomBgAnimationDrawable(this, binding.animationbg2),
            wordDrawable = HdmiByomWordAppearAnimationDrawable(this, binding.animationword2)
        )
    }

    //Byom - Hdmi 状态结束动画
    private fun stopHdmiByomAnimation() {
        if (!isStop) {
            logTagD(TAG, "Byom - Hdmi 状态结束动画")
            setSmallRoundScreenLight(true)
            checkHdmiOutAndSetLowLight()
            runOnUiThread {
                val animation2 =
                    HdmiByomWordDisappearAnimationDrawable(
                        this,
                        binding.animationword2,
                        onAnimationEnd = {
                            binding.animationbg2.visibility = View.INVISIBLE
                            binding.animationword1.visibility = View.VISIBLE
                        })
                wordPlayer2.play(animation2)
            }
        }
    }

    //Byom - USB 状态中动画
    private fun startUsbByomAnimation() {
        logTagD(TAG, "Byom - USB 状态中动画")
        setSmallRoundScreenLight(true)
        playOverlayAnimation(
            bgDrawable = UsbByomBgAnimationDrawable(this, binding.animationbg2),
            wordDrawable = UsbByomWordAppearAnimationDrawable(this, binding.animationword2)
        )
    }

    private fun playOverlayAnimation(
        bgDrawable: BaseVideoAnimation,
        wordDrawable: BaseHdmiAnimationDrawable
    ) {
        if (!isStop) {
            logTagD(TAG, "播放覆盖动画")
            runOnUiThread {
                binding.animationbg2.visibility = View.VISIBLE
                binding.animationword1.visibility = View.INVISIBLE
                val animation2 = wordDrawable
                wordPlayer2.play(animation2)
                val animation1 = bgDrawable
                bgPlayer2.play(animation1)
            }
        }
    }

    //Byom - USB 状态结束动画
    private fun stopUSBByomAnimation() {
        if (!isStop) {
            logTagD(TAG, "Byom - USB 状态结束动画")
            setSmallRoundScreenLight(true)
            checkHdmiOutAndSetLowLight()
            runOnUiThread {
                val animation2 =
                    UsbByomWordDisappearAnimationDrawable(
                        this,
                        binding.animationword2,
                        onAnimationEnd = {
                            binding.animationbg2.visibility = View.INVISIBLE
                            binding.animationword1.visibility = View.VISIBLE
                        })
                wordPlayer2.play(animation2)
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return false
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus && isFirstLaunch) {
            logTagD(TAG, "AnimationActivity 已经显示到屏幕上")
            isFirstLaunch = false
            // 在这里执行需要在 Activity 显示到屏幕后执行的逻辑 还是需要延迟一会
            launch {
                kotlinx.coroutines.delay(2000)
                getHdmiOutState()
            }
        }
    }

    // 检查HDMI OUT状态并设置低亮模式
    private fun checkHdmiOutAndSetLowLight() {
        if (hdmiOutState == HdmiOutStateEnum.HDMI_OUT_STATE_DISCONNECTED) {
            logTagD(TAG, "HDMI OUT未连接，延迟设置低亮模式")
            setLowLightJob = launch {
                kotlinx.coroutines.delay(ONE_MIN)
                setSmallRoundScreenLight(false) // 设置低亮模式
            }
        }
    }

    /**
     * 设置屏幕亮度
     */
    private fun setSmallRoundScreenLight(isBright: Boolean) {

        if (isBright) {
            // 如果设置为高亮，取消延迟设置低亮模式的任务
            logTagD(TAG, "取消延迟设置低亮模式的任务")
            setLowLightJob?.cancel()
        }

        val brightness = if (isBright) BRIGHTNESS_HIGH_LIGHT else BRIGHTNESS_LOW_LIGHT
        logTagD(TAG, "设置屏幕亮度: $brightness")

        launch {
            try {
                val success = setScreenBrightness(brightness)
                if (success) {
                    logTagD(TAG, "屏幕亮度设置成功: $brightness")
                } else {
                    logTagE(TAG, "屏幕亮度设置失败: $brightness")
                }
            } catch (e: Exception) {
                logTagE(TAG, "设置屏幕亮度异常", tr = e)
            }
        }
    }

    /**
     * 设置屏幕亮度
     * @param brightness 亮度值，范围 [BRIGHTNESS_MIN, BRIGHTNESS_MAX]
     * @return 设置是否成功
     */
    private suspend fun setScreenBrightness(brightness: Int): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            // 首先设置亮度模式为手动模式
            Settings.System.putInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS_MODE,
                Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL
            )

            // 设置亮度值
            val result = Settings.System.putInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                brightness
            )

            logTagD(TAG, "设置屏幕亮度结果: $result, 亮度值: $brightness")
            result
        } catch (e: Exception) {
            logTagE(TAG, "设置屏幕亮度异常", tr = e)
            false
        }
    }
}

