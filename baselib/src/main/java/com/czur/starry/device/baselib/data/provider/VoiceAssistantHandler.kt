package com.czur.starry.device.baselib.data.provider

import androidx.lifecycle.MutableLiveData
import com.czur.starry.device.baselib.handler.SPContentHandler

/**
*  author : <PERSON><PERSON><PERSON>
*  time   :2025/06/09
*/


object VoiceAssistantHandler : SPContentHandler() {

    private const val TAG = "VoiceAssistantHandler"

    const val KEY_VOICE_ASSISTANT_TOTAL_SWITCH = "voice_assistant_total_switch" // 语音助手总开关
    const val KEY_VOICE_ASSISTANT_IS_SUPPORT = "voice_assistant_is_support_scene" // 当前场景是否支持语音助手

    override val keyLiveMap: Map<String, LiveTrans<*>> by lazy {
        mapOf(
            KEY_VOICE_ASSISTANT_TOTAL_SWITCH to LiveTrans(isVoiceAssistantEnabledLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_VOICE_ASSISTANT_IS_SUPPORT to LiveTrans(isVoiceAssistantSupportLive as MutableLiveData) {
                it.toBoolean()
            }
        )
    }

    override val authority: String =
        "com.czur.starry.device.voiceassistant.provider.initProvider"

    //语音助手总开关
    val isVoiceAssistantEnabledLive by lazy { createLive { isVoiceAssistantEnabled } }
    var isVoiceAssistantEnabled: Boolean
        set(value) {
            setValue(KEY_VOICE_ASSISTANT_TOTAL_SWITCH, value)
        }
        get() = getValue(KEY_VOICE_ASSISTANT_TOTAL_SWITCH, true)

    //当前场景是否支持语音助手
    val isVoiceAssistantSupportLive by lazy { createLive { isVoiceAssistantSupport } }
    var isVoiceAssistantSupport: Boolean
        set(value) {
            setValue(KEY_VOICE_ASSISTANT_IS_SUPPORT, value)
        }
        get() = getValue(KEY_VOICE_ASSISTANT_IS_SUPPORT, true)
}